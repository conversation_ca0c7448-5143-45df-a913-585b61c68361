#!/usr/bin/env python3
# ABOUTME: Diagnostic script to understand the type error in dividend ingestion
# ABOUTME: Tests the exact scenario that's causing the error

import pandas as pd
from datetime import datetime

# Simulate what happens in the dividend ingestion
def test_dividend_ingestion():
    print("Testing dividend ingestion scenario...")
    
    # Initialize like in the method
    total_records = 0
    print(f"Initial total_records: {total_records} (type: {type(total_records)})")
    
    # Create a mock dividend dataframe
    dividends = pd.DataFrame({
        'code': ['1234', '5678'],
        'date': ['2024-01-01', '2024-01-02'],
        'dividend': [100, 200]
    })
    print(f"\nCreated dividend DataFrame with {len(dividends)} rows")
    
    # Simulate the count assignment
    count = len(dividends)
    print(f"count = len(dividends): {count} (type: {type(count)})")
    
    # Try the addition
    try:
        total_records += count
        print(f"Success! total_records after addition: {total_records}")
    except TypeError as e:
        print(f"ERROR: {e}")
        print(f"This is what we're seeing in the logs!")
        
    # Now let's see what could make count a string
    print("\n--- Testing potential issues ---")
    
    # What if insert_dataframe returned a string and we mistakenly used it?
    job_id = "5c379260-c7db-4ad7-ab42-5d3ee45ea8fb"
    print(f"job_id from insert_dataframe: {job_id} (type: {type(job_id)})")
    
    # If we accidentally did this:
    try:
        wrong_total = 0
        wrong_total += job_id
    except TypeError as e:
        print(f"If we add job_id to int: {e}")
        
    # The actual code seems correct, so let's check if there's a hidden issue
    print("\n--- Checking our fixed code ---")
    print("The code does:")
    print("  job_id = self.bigquery_client.insert_dataframe(...)")
    print("  count = len(dividends)")
    print("  total_records += count")
    print("\nThis should work fine unless 'count' is being overwritten somewhere")

if __name__ == "__main__":
    test_dividend_ingestion()