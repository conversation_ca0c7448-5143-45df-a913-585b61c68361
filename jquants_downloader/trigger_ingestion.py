#!/usr/bin/env python3
# ABOUTME: <PERSON>ript to trigger ingestion with proper authentication and error handling
# ABOUTME: Shows the response and starts monitoring if successful

import subprocess
import requests
import json
import time

def get_auth_token():
    """Get authentication token."""
    try:
        result = subprocess.run(
            ['gcloud', 'auth', 'print-identity-token'],
            capture_output=True,
            text=True,
            timeout=10
        )
        if result.returncode == 0:
            return result.stdout.strip()
    except Exception as e:
        print(f"Error getting auth token: {e}")
    return None

def trigger_ingestion(mode="daily-update"):
    """Trigger the ingestion service."""
    base_url = "https://jquants-ingestion-621634133093.asia-northeast1.run.app"
    
    print("🔑 Getting authentication token...")
    token = get_auth_token()
    
    if not token:
        print("❌ Failed to get authentication token")
        return False
        
    print("✅ Got authentication token")
    
    print(f"\n🚀 Triggering ingestion with mode: {mode}")
    print("   This may take a while as the service processes the request...")
    
    try:
        response = requests.post(
            f"{base_url}/trigger",
            headers={
                'Authorization': f'Bearer {token}',
                'Content-Type': 'application/json'
            },
            json={"mode": mode},
            timeout=300  # 5 minute timeout
        )
        
        print(f"\n📡 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Successfully triggered ingestion!")
            
            try:
                data = response.json()
                print("\n📋 Response Data:")
                print(json.dumps(data, indent=2))
            except:
                print(f"   Response: {response.text}")
                
            return True
        else:
            print(f"❌ Failed to trigger ingestion")
            print(f"   Response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("⏱️  Request timed out (this might mean ingestion is running)")
        print("   Check the monitor logs for progress")
        return True
    except Exception as e:
        print(f"❌ Error triggering ingestion: {e}")
        return False

def main():
    print("=" * 60)
    print("J-QUANTS INGESTION TRIGGER")
    print("=" * 60)
    
    success = trigger_ingestion()
    
    if success:
        print("\n📊 Ingestion has been triggered!")
        print("   Monitor progress with:")
        print("   - tail -f ingestion_monitor.log")
        print("   - python check_status.py")
        print("   - cat ingestion_status.json | jq .")
    else:
        print("\n❌ Failed to trigger ingestion")
        print("   Check Cloud Run logs for details:")
        print("   gcloud run services logs read jquants-ingestion --region=asia-northeast1")

if __name__ == "__main__":
    main()