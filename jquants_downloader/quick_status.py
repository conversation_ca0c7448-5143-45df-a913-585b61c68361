#!/usr/bin/env python3
"""Quick status check - shows current BigQuery data without complex queries."""

from google.cloud import bigquery

def quick_status():
    client = bigquery.Client(project='tokyotickers')
    
    print(f"\n📊 QUICK STATUS CHECK - {__import__('datetime').datetime.now().strftime('%H:%M:%S')}")
    print("="*60)
    
    # Simple table row counts
    try:
        tables = ['companies', 'daily_prices', 'dividends', 'margin_trading', 'topix_data']
        
        for table in tables:
            try:
                query = f"SELECT COUNT(*) as count FROM `tokyotickers.jquants_data.{table}`"
                result = list(client.query(query))[0]
                print(f"{table:<20} {result.count:>10,} rows")
            except Exception as e:
                if "not found" in str(e).lower():
                    print(f"{table:<20}       No table")
                else:
                    print(f"{table:<20}       Error")
    except Exception as e:
        print(f"Error: {e}")
    
    print("="*60)

if __name__ == "__main__":
    quick_status()