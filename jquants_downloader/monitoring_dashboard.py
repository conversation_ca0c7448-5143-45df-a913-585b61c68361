#!/usr/bin/env python3
"""
Real-time monitoring dashboard for J-Quants data ingestion.
Serves a web dashboard that auto-updates every 30 seconds.
"""

import json
from datetime import datetime, timedelta
from flask import Flask, render_template_string, jsonify
from google.cloud import bigquery
import threading
import time

app = Flask(__name__)

# Global data cache
dashboard_data = {
    'last_update': None,
    'tables': [],
    'jobs': {'success': 0, 'failed': 0},
    'latest_data': {},
    'ingestion_rates': {},
    'coverage': {},
    'errors': []
}

# HTML Template
DASHBOARD_HTML = """
<!DOCTYPE html>
<html>
<head>
    <title>J-Quants Data Ingestion Monitor</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        h1 {
            color: #333;
            margin-bottom: 30px;
        }
        .update-time {
            color: #666;
            font-size: 14px;
            margin-bottom: 20px;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .card h2 {
            margin-top: 0;
            color: #444;
            font-size: 18px;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        .metric {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .metric:last-child {
            border-bottom: none;
        }
        .metric-label {
            color: #666;
        }
        .metric-value {
            font-weight: 600;
            color: #333;
        }
        .success {
            color: #10b981;
        }
        .failed {
            color: #ef4444;
        }
        .warning {
            color: #f59e0b;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            text-align: left;
            padding: 8px;
            border-bottom: 1px solid #eee;
        }
        th {
            background: #f9f9f9;
            font-weight: 600;
            color: #666;
        }
        .number {
            text-align: right;
            font-family: monospace;
        }
        .error-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .error-item {
            padding: 8px;
            margin: 4px 0;
            background: #fee;
            border-left: 3px solid #ef4444;
            border-radius: 4px;
            font-size: 14px;
        }
        .loading {
            opacity: 0.6;
        }
        .auto-refresh {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #333;
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 J-Quants Data Ingestion Monitor</h1>
        <div class="update-time" id="update-time">Loading...</div>
        
        <div class="grid">
            <!-- Summary Card -->
            <div class="card">
                <h2>📈 Summary</h2>
                <div id="summary-metrics">
                    <div class="metric">
                        <span class="metric-label">Total Tables</span>
                        <span class="metric-value" id="total-tables">-</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Total Records</span>
                        <span class="metric-value" id="total-records">-</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Total Size</span>
                        <span class="metric-value" id="total-size">-</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Jobs (24h)</span>
                        <span class="metric-value">
                            <span class="success" id="jobs-success">0</span> / 
                            <span class="failed" id="jobs-failed">0</span>
                        </span>
                    </div>
                </div>
            </div>
            
            <!-- Coverage Card -->
            <div class="card">
                <h2>📊 Data Coverage</h2>
                <div id="coverage-metrics">
                    <div class="metric">
                        <span class="metric-label">Companies</span>
                        <span class="metric-value" id="total-companies">-</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">With Prices</span>
                        <span class="metric-value" id="price-coverage">-</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">With Dividends</span>
                        <span class="metric-value" id="dividend-coverage">-</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">With Financials</span>
                        <span class="metric-value" id="financial-coverage">-</span>
                    </div>
                </div>
            </div>
            
            <!-- Ingestion Rate Card -->
            <div class="card">
                <h2>⚡ Ingestion Rate (1h)</h2>
                <div id="rate-metrics">
                    <div class="metric">
                        <span class="metric-label">Daily Prices</span>
                        <span class="metric-value" id="rate-prices">-</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Dividends</span>
                        <span class="metric-value" id="rate-dividends">-</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Financials</span>
                        <span class="metric-value" id="rate-financials">-</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Other</span>
                        <span class="metric-value" id="rate-other">-</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Tables List -->
        <div class="card">
            <h2>📋 Table Details</h2>
            <table>
                <thead>
                    <tr>
                        <th>Table</th>
                        <th class="number">Records</th>
                        <th class="number">Size (GB)</th>
                        <th>Latest Data</th>
                        <th>Freshness</th>
                    </tr>
                </thead>
                <tbody id="tables-body">
                    <tr><td colspan="5">Loading...</td></tr>
                </tbody>
            </table>
        </div>
        
        <!-- Recent Errors -->
        <div class="card" id="errors-card" style="display: none;">
            <h2>❌ Recent Errors</h2>
            <ul class="error-list" id="errors-list"></ul>
        </div>
    </div>
    
    <div class="auto-refresh">
        🔄 Auto-refresh: <span id="countdown">30</span>s
    </div>
    
    <script>
        let countdown = 30;
        
        function formatNumber(num) {
            return new Intl.NumberFormat('en-US').format(num);
        }
        
        function formatBytes(bytes) {
            const gb = bytes / 1e9;
            return gb.toFixed(2) + ' GB';
        }
        
        function getFreshnessColor(hours) {
            if (hours < 24) return 'success';
            if (hours < 48) return 'warning';
            return 'failed';
        }
        
        function updateDashboard() {
            fetch('/api/data')
                .then(response => response.json())
                .then(data => {
                    // Update time
                    document.getElementById('update-time').textContent = 
                        'Last updated: ' + new Date(data.last_update).toLocaleString();
                    
                    // Summary metrics
                    document.getElementById('total-tables').textContent = data.tables.length;
                    document.getElementById('total-records').textContent = 
                        formatNumber(data.tables.reduce((sum, t) => sum + t.row_count, 0));
                    document.getElementById('total-size').textContent = 
                        formatBytes(data.tables.reduce((sum, t) => sum + t.size_bytes, 0));
                    document.getElementById('jobs-success').textContent = data.jobs.success;
                    document.getElementById('jobs-failed').textContent = data.jobs.failed;
                    
                    // Coverage metrics
                    if (data.coverage.total_companies) {
                        document.getElementById('total-companies').textContent = 
                            formatNumber(data.coverage.total_companies);
                        document.getElementById('price-coverage').textContent = 
                            formatNumber(data.coverage.companies_with_prices) + 
                            ' (' + data.coverage.price_coverage_pct + '%)';
                        document.getElementById('dividend-coverage').textContent = 
                            formatNumber(data.coverage.companies_with_dividends) + 
                            ' (' + data.coverage.dividend_coverage_pct + '%)';
                        document.getElementById('financial-coverage').textContent = 
                            formatNumber(data.coverage.companies_with_financials) + 
                            ' (' + data.coverage.financial_coverage_pct + '%)';
                    }
                    
                    // Ingestion rates
                    document.getElementById('rate-prices').textContent = 
                        data.ingestion_rates.daily_prices || '-';
                    document.getElementById('rate-dividends').textContent = 
                        data.ingestion_rates.dividends || '-';
                    document.getElementById('rate-financials').textContent = 
                        data.ingestion_rates.financial_statements || '-';
                    document.getElementById('rate-other').textContent = 
                        data.ingestion_rates.other || '-';
                    
                    // Tables
                    const tbody = document.getElementById('tables-body');
                    tbody.innerHTML = data.tables.map(table => {
                        const latest = data.latest_data[table.table_name];
                        const freshnessClass = getFreshnessColor(latest ? latest.hours_ago : 999);
                        return `
                            <tr>
                                <td>${table.table_name}</td>
                                <td class="number">${formatNumber(table.row_count)}</td>
                                <td class="number">${table.size_gb.toFixed(2)}</td>
                                <td>${latest ? latest.date : 'No data'}</td>
                                <td class="${freshnessClass}">
                                    ${latest ? latest.hours_ago + 'h ago' : '-'}
                                </td>
                            </tr>
                        `;
                    }).join('');
                    
                    // Errors
                    if (data.errors.length > 0) {
                        document.getElementById('errors-card').style.display = 'block';
                        document.getElementById('errors-list').innerHTML = 
                            data.errors.map(e => `<li class="error-item">${e}</li>`).join('');
                    } else {
                        document.getElementById('errors-card').style.display = 'none';
                    }
                    
                    // Reset countdown
                    countdown = 30;
                })
                .catch(error => {
                    console.error('Error fetching data:', error);
                });
        }
        
        // Update countdown
        setInterval(() => {
            countdown--;
            if (countdown <= 0) {
                updateDashboard();
            }
            document.getElementById('countdown').textContent = countdown;
        }, 1000);
        
        // Initial load
        updateDashboard();
    </script>
</body>
</html>
"""

def update_data():
    """Background thread to update dashboard data."""
    client = bigquery.Client(project='tokyotickers')
    
    while True:
        try:
            data = {
                'last_update': datetime.now().isoformat(),
                'tables': [],
                'jobs': {'success': 0, 'failed': 0},
                'latest_data': {},
                'ingestion_rates': {},
                'coverage': {},
                'errors': []
            }
            
            # Get table stats
            query = """
            SELECT 
                table_id as table_name,
                row_count,
                size_bytes,
                ROUND(size_bytes / POW(10, 9), 2) as size_gb
            FROM `tokyotickers.jquants_data.__TABLES__`
            ORDER BY row_count DESC
            """
            
            for row in client.query(query):
                data['tables'].append({
                    'table_name': row.table_name,
                    'row_count': row.row_count,
                    'size_bytes': row.size_bytes,
                    'size_gb': row.size_gb
                })
            
            # Get job stats
            query = """
            SELECT 
                CASE WHEN error_result IS NULL THEN 'SUCCESS' ELSE 'FAILED' END as status,
                COUNT(*) as count
            FROM `tokyotickers.region-asia-northeast1.INFORMATION_SCHEMA.JOBS_BY_PROJECT`
            WHERE creation_time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 24 HOUR)
                AND job_type = 'LOAD'
                AND destination_table.dataset_id = 'jquants_data'
            GROUP BY status
            """
            
            for row in client.query(query):
                if row.status == 'SUCCESS':
                    data['jobs']['success'] = row.count
                else:
                    data['jobs']['failed'] = row.count
            
            # Get latest data dates
            tables_dates = [
                ('daily_prices', 'date'),
                ('dividends', 'AnnouncementDate'),
                ('margin_trading', 'Date'),
                ('topix_data', 'Date')
            ]
            
            for table, date_col in tables_dates:
                try:
                    query = f"""
                    SELECT 
                        MAX({date_col}) as latest_date,
                        TIMESTAMP_DIFF(CURRENT_TIMESTAMP(), MAX({date_col}), HOUR) as hours_ago
                    FROM `tokyotickers.jquants_data.{table}`
                    """
                    result = list(client.query(query))[0]
                    if result.latest_date:
                        data['latest_data'][table] = {
                            'date': str(result.latest_date),
                            'hours_ago': result.hours_ago
                        }
                except:
                    pass
            
            # Get ingestion rates (last hour)
            for table in ['daily_prices', 'dividends']:
                try:
                    query = f"""
                    SELECT COUNT(*) as count
                    FROM `tokyotickers.jquants_data.{table}`
                    WHERE ingestion_timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 HOUR)
                    """
                    result = list(client.query(query))[0]
                    if result.count > 0:
                        data['ingestion_rates'][table] = f"{result.count:,}/hr"
                except:
                    pass
            
            # Get data coverage
            try:
                query = """
                WITH company_counts AS (
                    SELECT COUNT(DISTINCT Code) as total_companies 
                    FROM `tokyotickers.jquants_data.companies`
                ),
                price_coverage AS (
                    SELECT COUNT(DISTINCT code) as companies_with_prices
                    FROM `tokyotickers.jquants_data.daily_prices`
                ),
                dividend_coverage AS (
                    SELECT COUNT(DISTINCT Code) as companies_with_dividends
                    FROM `tokyotickers.jquants_data.dividends`
                )
                SELECT 
                    c.total_companies,
                    p.companies_with_prices,
                    ROUND(p.companies_with_prices / c.total_companies * 100, 1) as price_coverage_pct,
                    d.companies_with_dividends,
                    ROUND(d.companies_with_dividends / c.total_companies * 100, 1) as dividend_coverage_pct
                FROM company_counts c
                CROSS JOIN price_coverage p
                CROSS JOIN dividend_coverage d
                """
                
                result = list(client.query(query))[0]
                data['coverage'] = {
                    'total_companies': result.total_companies,
                    'companies_with_prices': result.companies_with_prices,
                    'price_coverage_pct': result.price_coverage_pct,
                    'companies_with_dividends': result.companies_with_dividends,
                    'dividend_coverage_pct': result.dividend_coverage_pct,
                    'companies_with_financials': 0,
                    'financial_coverage_pct': 0
                }
            except:
                pass
            
            # Get recent errors
            query = """
            SELECT 
                error_result.message as error,
                destination_table.table_id as table_name,
                creation_time
            FROM `tokyotickers.region-asia-northeast1.INFORMATION_SCHEMA.JOBS_BY_PROJECT`
            WHERE creation_time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 6 HOUR)
                AND job_type = 'LOAD'
                AND destination_table.dataset_id = 'jquants_data'
                AND error_result IS NOT NULL
            ORDER BY creation_time DESC
            LIMIT 5
            """
            
            for row in client.query(query):
                data['errors'].append(f"{row.table_name}: {row.error}")
            
            # Update global data
            global dashboard_data
            dashboard_data = data
            
        except Exception as e:
            print(f"Error updating data: {e}")
        
        # Wait 30 seconds before next update
        time.sleep(30)

@app.route('/')
def index():
    return render_template_string(DASHBOARD_HTML)

@app.route('/api/data')
def api_data():
    return jsonify(dashboard_data)

if __name__ == '__main__':
    # Start background data updater
    updater = threading.Thread(target=update_data, daemon=True)
    updater.start()
    
    # Wait for initial data
    print("🚀 Starting J-Quants Monitoring Dashboard...")
    print("⏳ Loading initial data...")
    time.sleep(3)
    
    print("\n✅ Dashboard ready!")
    print("🌐 Open http://localhost:5001 in your browser")
    print("🔄 Auto-refreshes every 30 seconds\n")
    
    # Run Flask app
    app.run(host='0.0.0.0', port=5001, debug=False)