#!/usr/bin/env python3
"""GCP Account Manager for handling multiple Google accounts."""

import subprocess
import sys
import json
from typing import List, Dict, Optional


class GCPAccountManager:
    """Manages multiple GCP accounts and authentication."""
    
    def __init__(self):
        """Initialize GCP account manager."""
        pass
    
    def run_command(self, command: List[str], check: bool = True) -> subprocess.CompletedProcess:
        """Run a shell command and return the result.
        
        Args:
            command: Command to run as list of strings
            check: Whether to raise exception on non-zero exit code
            
        Returns:
            CompletedProcess object
        """
        print(f"Running: {' '.join(command)}")
        try:
            result = subprocess.run(command, capture_output=True, text=True, check=check)
            if result.stdout:
                print(f"Output: {result.stdout.strip()}")
            return result
        except subprocess.CalledProcessError as e:
            print(f"Error: {e}")
            if e.stderr:
                print(f"Error output: {e.stderr.strip()}")
            raise
    
    def list_authenticated_accounts(self) -> List[Dict[str, str]]:
        """List all authenticated Google accounts.
        
        Returns:
            List of account dictionaries with 'account' and 'status' keys
        """
        try:
            result = self.run_command(["gcloud", "auth", "list", "--format=json"], check=False)
            if result.returncode == 0:
                accounts = json.loads(result.stdout)
                return accounts
        except Exception as e:
            print(f"Error listing accounts: {e}")
        return []
    
    def get_active_account(self) -> Optional[str]:
        """Get the currently active Google account.
        
        Returns:
            Active account email or None if no active account
        """
        accounts = self.list_authenticated_accounts()
        for account in accounts:
            if account.get("status") == "ACTIVE":
                return account.get("account")
        return None
    
    def authenticate_account(self, account_email: str) -> bool:
        """Authenticate a specific Google account.
        
        Args:
            account_email: Email of the account to authenticate
            
        Returns:
            True if successful, False otherwise
        """
        print(f"Authenticating account: {account_email}")
        
        try:
            # First, try to activate if already authenticated
            if self.activate_account(account_email):
                return True
            
            # If not authenticated, run auth login
            print(f"Account {account_email} not found. Starting authentication...")
            result = self.run_command([
                "gcloud", "auth", "login", 
                "--account", account_email,
                "--brief"
            ])
            
            if result.returncode == 0:
                print(f"Successfully authenticated {account_email}")
                return True
            else:
                print(f"Failed to authenticate {account_email}")
                return False
                
        except Exception as e:
            print(f"Error authenticating account {account_email}: {e}")
            return False
    
    def activate_account(self, account_email: str) -> bool:
        """Activate an already authenticated account.
        
        Args:
            account_email: Email of the account to activate
            
        Returns:
            True if successful, False otherwise
        """
        accounts = self.list_authenticated_accounts()
        account_emails = [acc.get("account") for acc in accounts]
        
        if account_email not in account_emails:
            print(f"Account {account_email} is not authenticated")
            return False
        
        try:
            result = self.run_command([
                "gcloud", "config", "set", "account", account_email
            ])
            
            if result.returncode == 0:
                print(f"Activated account: {account_email}")
                return True
            else:
                print(f"Failed to activate account: {account_email}")
                return False
                
        except Exception as e:
            print(f"Error activating account {account_email}: {e}")
            return False
    
    def setup_account_for_project(self, account_email: str, project_id: str) -> bool:
        """Set up a specific account for a project.
        
        Args:
            account_email: Email of the Google account
            project_id: GCP project ID
            
        Returns:
            True if successful, False otherwise
        """
        print(f"Setting up account {account_email} for project {project_id}")
        
        # Step 1: Authenticate the account
        if not self.authenticate_account(account_email):
            return False
        
        # Step 2: Set the project
        try:
            result = self.run_command([
                "gcloud", "config", "set", "project", project_id
            ])
            
            if result.returncode == 0:
                print(f"Set project to {project_id}")
                return True
            else:
                print(f"Failed to set project to {project_id}")
                return False
                
        except Exception as e:
            print(f"Error setting project: {e}")
            return False
    
    def show_current_config(self) -> None:
        """Show current gcloud configuration."""
        print("Current gcloud configuration:")
        print("-" * 30)
        
        try:
            # Show active account
            active_account = self.get_active_account()
            print(f"Active Account: {active_account or 'None'}")
            
            # Show active project
            result = self.run_command([
                "gcloud", "config", "get-value", "project"
            ], check=False)
            
            if result.returncode == 0 and result.stdout.strip():
                project = result.stdout.strip()
                print(f"Active Project: {project}")
            else:
                print("Active Project: None")
                
        except Exception as e:
            print(f"Error getting configuration: {e}")
    
    def list_accounts_info(self) -> None:
        """List all authenticated accounts with detailed info."""
        accounts = self.list_authenticated_accounts()
        
        print("Authenticated Google Accounts:")
        print("=" * 40)
        
        if not accounts:
            print("No authenticated accounts found.")
            print("Run: gcloud auth login")
            return
        
        for account in accounts:
            email = account.get("account", "Unknown")
            status = account.get("status", "Unknown")
            status_indicator = "✓" if status == "ACTIVE" else " "
            print(f"{status_indicator} {email} ({status})")


def main():
    """Main function for GCP account manager CLI."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Manage multiple GCP accounts")
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # List command
    subparsers.add_parser("list", help="List all authenticated accounts")
    
    # Auth command
    auth_parser = subparsers.add_parser("auth", help="Authenticate an account")
    auth_parser.add_argument("account_email", help="Account email to authenticate")
    
    # Activate command
    activate_parser = subparsers.add_parser("activate", help="Activate an account")
    activate_parser.add_argument("account_email", help="Account email to activate")
    
    # Setup command
    setup_parser = subparsers.add_parser("setup", help="Setup account for project")
    setup_parser.add_argument("account_email", help="Account email")
    setup_parser.add_argument("project_id", help="GCP project ID")
    
    # Current command
    subparsers.add_parser("current", help="Show current configuration")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    manager = GCPAccountManager()
    
    if args.command == "list":
        manager.list_accounts_info()
    elif args.command == "auth":
        success = manager.authenticate_account(args.account_email)
        sys.exit(0 if success else 1)
    elif args.command == "activate":
        success = manager.activate_account(args.account_email)
        sys.exit(0 if success else 1)
    elif args.command == "setup":
        success = manager.setup_account_for_project(args.account_email, args.project_id)
        sys.exit(0 if success else 1)
    elif args.command == "current":
        manager.show_current_config()


if __name__ == "__main__":
    main()
