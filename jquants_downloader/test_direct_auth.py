#!/usr/bin/env python3
"""Test direct authentication without refresh token."""

from jquants_client import JQuantsClient
from dotenv import load_dotenv
import os

def test_direct_auth():
    """Test getting a fresh refresh token and then using it."""
    load_dotenv()
    
    email = os.getenv("JQUANTS_EMAIL", "").strip('"\'')
    password = os.getenv("JQUANTS_PASSWORD", "").strip('"\'')
    
    print(f"Testing direct authentication with email: {email}")
    
    # Create client without refresh token
    client = JQuantsClient(email, password)
    
    try:
        # Get fresh refresh token
        print("Getting fresh refresh token...")
        refresh_token = client.get_refresh_token()
        print(f"✅ Got refresh token: {refresh_token[:50]}...")
        
        # Now try to get ID token
        print("Getting ID token...")
        id_token = client.get_id_token()
        print(f"✅ Got ID token: {id_token[:50]}...")
        
        # Try to get companies data
        print("Getting companies data...")
        companies = client.get_listed_companies()
        print(f"✅ Got {len(companies)} companies!")
        
        if companies:
            print(f"First company: {companies[0].code} - {companies[0].company_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = test_direct_auth()
    if success:
        print("\n🎉 Direct authentication successful!")
    else:
        print("\n❌ Direct authentication failed!")
