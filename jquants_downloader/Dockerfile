# Production Dockerfile for J-Quants Data Ingestion Service
FROM python:3.12-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Set environment variables
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# Create non-root user
RUN useradd --create-home --shell /bin/bash jquants
RUN chown -R jquants:jquants /app
USER jquants

# Expose port for Cloud Run
EXPOSE 8080

# Default command for Cloud Run (web service)
CMD ["python", "web_service.py"]
