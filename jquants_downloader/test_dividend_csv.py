#!/usr/bin/env python3

import os
from datetime import datetime, timedelta
from jquantsapi import Client

# Initialize J-Quants client
email = os.getenv('JQUANTS_EMAIL', 'and<PERSON><PERSON><PERSON>@gmail.com')
password = os.getenv('JQUANTS_PASSWORD', 'Borboletas747')

client = Client(mail_address=email, password=password)

# Get a small sample of dividend data (just last 7 days)
end_date = datetime.now()
start_date = end_date - timedelta(days=7)

print(f"Getting dividend data from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")

dividends = client.get_dividend_range(
    start_dt=start_date.strftime('%Y-%m-%d'),
    end_dt=end_date.strftime('%Y-%m-%d')
)

print(f"Retrieved {len(dividends)} dividend records")

if len(dividends) > 0:
    # Save to CSV
    dividends.to_csv('dividend_sample.csv', index=False)
    print(f"Saved to dividend_sample.csv")
    print(f"Columns: {list(dividends.columns)}")
else:
    print("No dividend data found for this period")
