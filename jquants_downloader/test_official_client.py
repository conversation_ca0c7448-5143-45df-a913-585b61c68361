#!/usr/bin/env python3
"""Test the official J-Quants client."""

from jquantsapi import Client
from dotenv import load_dotenv
import os

def test_official_client():
    """Test the official J-Quants client."""
    load_dotenv()
    
    email = os.getenv("JQUANTS_EMAIL", "").strip('"\'')
    password = os.getenv("JQUANTS_PASSWORD", "").strip('"\'')
    
    print(f"Testing official J-Quants client with email: {email}")
    
    try:
        # Create client with email and password
        client = Client(mail_address=email, password=password)
        
        print("✅ Client created successfully")
        
        # Try to get listed companies
        print("Getting listed companies...")
        companies_df = client.get_listed_info()
        
        print(f"✅ Got {len(companies_df)} companies!")
        print(f"Columns: {list(companies_df.columns)}")
        
        if len(companies_df) > 0:
            print("\nFirst few companies:")
            print(companies_df.head())
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_official_client()
    if success:
        print("\n🎉 Official client test successful!")
    else:
        print("\n❌ Official client test failed!")
