#!/usr/bin/env python3
# ABOUTME: Quick test script to verify Cloud Run authentication is working
# ABOUTME: Tests both health check and trigger endpoints with proper authentication

import subprocess
import requests
import json

def get_auth_token():
    """Get authentication token for Cloud Run service."""
    try:
        result = subprocess.run(
            ['gcloud', 'auth', 'print-identity-token'],
            capture_output=True,
            text=True,
            timeout=10
        )
        if result.returncode == 0:
            return result.stdout.strip()
        else:
            print(f"Failed to get auth token: {result.stderr}")
            return None
    except Exception as e:
        print(f"Error getting auth token: {e}")
        return None

def test_endpoints():
    """Test various Cloud Run endpoints with authentication."""
    base_url = "https://jquants-ingestion-621634133093.asia-northeast1.run.app"
    
    # Get auth token
    print("🔑 Getting authentication token...")
    token = get_auth_token()
    
    if not token:
        print("❌ Failed to get authentication token")
        return
    
    print("✅ Got authentication token")
    print(f"   Token preview: {token[:20]}...{token[-20:]}")
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    # Test health endpoint
    print("\n📋 Testing health endpoint...")
    try:
        response = requests.get(f"{base_url}/", headers=headers, timeout=10)
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text[:100]}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test status endpoint
    print("\n📊 Testing status endpoint...")
    try:
        response = requests.get(f"{base_url}/status", headers=headers, timeout=10)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Response: {json.dumps(data, indent=2)}")
        else:
            print(f"   Response: {response.text[:200]}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test trigger endpoint (without actually triggering)
    print("\n🚀 Testing trigger endpoint (dry run)...")
    print("   Would send: POST /trigger with {'mode': 'daily-update'}")
    print("   Skipping actual trigger to avoid starting ingestion")
    
    print("\n✅ Authentication test complete!")

if __name__ == "__main__":
    test_endpoints()