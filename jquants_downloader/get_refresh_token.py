#!/usr/bin/env python3
"""Script to get a fresh J-Quants refresh token."""

import requests
import json
import sys
from dotenv import load_dotenv
import os

def get_refresh_token(email: str, password: str) -> str:
    """Get refresh token from J-Quants API using email and password.

    Args:
        email: J-Quants account email
        password: J-Quants account password

    Returns:
        Refresh token string
    """
    url = "https://api.jquants.com/v1/token/auth_user"

    data = {
        "mailaddress": email,
        "password": password
    }

    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }

    print(f"Getting refresh token for: {email}")

    try:
        response = requests.post(url, json=data, headers=headers)
        response.raise_for_status()

        result = response.json()

        if "refreshToken" in result:
            refresh_token = result["refreshToken"]
            print("✅ Successfully obtained refresh token!")
            print(f"Refresh token: {refresh_token[:50]}...")
            return refresh_token
        else:
            print("❌ No refresh token in response")
            print(f"Response: {result}")
            return None

    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        if hasattr(e, 'response') and e.response is not None:
            try:
                error_detail = e.response.json()
                print(f"Error details: {error_detail}")
            except:
                print(f"Error response: {e.response.text}")
        return None
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return None


def main():
    """Main function."""
    # Load current environment
    load_dotenv()

    email = os.getenv("JQUANTS_EMAIL")
    password = os.getenv("JQUANTS_PASSWORD")

    if not email or not password:
        print("❌ JQUANTS_EMAIL and JQUANTS_PASSWORD must be set in .env file")
        sys.exit(1)

    # Remove quotes if present
    email = email.strip('"\'')
    password = password.strip('"\'')

    print("J-Quants Refresh Token Generator")
    print("=" * 40)
    print(f"Email: {email}")
    print(f"Password: {'*' * len(password)}")
    print()

    # Try multiple email variations
    email_variations = [
        email,
        "<EMAIL>",  # Try the original email
        "<EMAIL>"  # Try the tokyotickers email
    ]

    for test_email in email_variations:
        print(f"Trying email: {test_email}")
        refresh_token = get_refresh_token(test_email, password)
        if refresh_token:
            break
    else:
        refresh_token = None

    if refresh_token:
        print("\n" + "=" * 60)
        print("SUCCESS! Copy this refresh token to your .env file:")
        print("=" * 60)
        print(f"JQUANTS_REFRESH_TOKEN=\"{refresh_token}\"")
        print("=" * 60)

        # Optionally update the .env file automatically
        update_env = input("\nDo you want to automatically update the .env file? (y/N): ").strip().lower()

        if update_env == 'y':
            try:
                # Read current .env file
                with open('.env', 'r') as f:
                    content = f.read()

                # Replace the refresh token line
                lines = content.split('\n')
                updated_lines = []

                for line in lines:
                    if line.startswith('JQUANTS_REFRESH_TOKEN='):
                        updated_lines.append(f'JQUANTS_REFRESH_TOKEN="{refresh_token}"')
                    else:
                        updated_lines.append(line)

                # Write back to .env file
                with open('.env', 'w') as f:
                    f.write('\n'.join(updated_lines))

                print("✅ .env file updated successfully!")

            except Exception as e:
                print(f"❌ Failed to update .env file: {e}")
                print("Please manually update the JQUANTS_REFRESH_TOKEN in your .env file")

        sys.exit(0)
    else:
        print("\n❌ Failed to get refresh token")
        print("Please check your email and password")
        sys.exit(1)


if __name__ == "__main__":
    main()
