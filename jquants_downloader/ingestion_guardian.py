#!/usr/bin/env python3
# ABOUTME: Continuous guardian service that monitors and ensures J-Quants ingestion never stops
# ABOUTME: Automatically triggers ingestion when idle, sleeps between checks, runs indefinitely

import time
import json
import subprocess
import requests
from datetime import datetime, timedelta
import logging
import signal
import sys
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ingestion_guardian.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class IngestionGuardian:
    def __init__(self):
        self.service_url = "https://jquants-ingestion-621634133093.asia-northeast1.run.app"
        self.status_file = Path("ingestion_status.json")
        self.last_progress_time = None
        self.last_record_count = 0
        self.consecutive_idle_checks = 0
        self.max_idle_minutes = 15  # Trigger after 15 minutes of no progress
        self.check_interval = 300  # Check every 5 minutes
        self.auth_token = None
        self.token_expiry = None
        self.running = True
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self.handle_shutdown)
        signal.signal(signal.SIGTERM, self.handle_shutdown)
        
    def handle_shutdown(self, signum, frame):
        logger.info("🛑 Shutdown signal received. Stopping guardian...")
        self.running = False
        sys.exit(0)
        
    def get_auth_token(self, force_refresh=False):
        """Get or refresh GCP authentication token"""
        if not force_refresh and self.auth_token and self.token_expiry and datetime.now() < self.token_expiry:
            return self.auth_token
            
        try:
            result = subprocess.run(
                ['gcloud', 'auth', 'print-identity-token'],
                capture_output=True,
                text=True,
                check=True
            )
            self.auth_token = result.stdout.strip()
            self.token_expiry = datetime.now() + timedelta(minutes=50)
            logger.info("🔑 Authentication token refreshed")
            return self.auth_token
        except Exception as e:
            logger.error(f"❌ Failed to get auth token: {e}")
            return None
            
    def get_bigquery_status(self):
        """Get current record counts from BigQuery"""
        try:
            from bigquery_client import BigQueryClient
            client = BigQueryClient(project_id='tokyotickers', dataset_id='jquants_data')
            
            tables = [
                'companies', 'daily_prices', 'dividends', 'financial_statements',
                'margin_trading', 'topix_data', 'indices_data', 'trading_by_type',
                'short_selling', 'breakdown_trading', 'financial_statements_detailed',
                'futures', 'options', 'index_options'
            ]
            
            total_records = 0
            table_counts = {}
            
            for table in tables:
                try:
                    result = client.client.query(
                        f"SELECT COUNT(*) as count FROM `{client.project_id}.{client.dataset_id}.{table}`"
                    ).result()
                    count = list(result)[0]['count']
                    table_counts[table] = count
                    total_records += count
                except:
                    table_counts[table] = 0
                    
            return total_records, table_counts
            
        except Exception as e:
            logger.error(f"❌ Failed to get BigQuery status: {e}")
            return 0, {}
            
    def check_ingestion_status(self):
        """Check if ingestion is making progress"""
        try:
            # Get BigQuery status
            total_records, table_counts = self.get_bigquery_status()
            
            # Check for progress
            progress_made = False
            if total_records > self.last_record_count:
                new_records = total_records - self.last_record_count
                logger.info(f"✅ Progress detected: {new_records:,} new records")
                
                # Log which tables grew
                if self.last_record_count > 0:
                    for table, count in table_counts.items():
                        old_count = getattr(self, f'last_{table}_count', 0)
                        if count > old_count:
                            logger.info(f"  - {table}: +{count - old_count:,}")
                        setattr(self, f'last_{table}_count', count)
                        
                self.last_record_count = total_records
                self.last_progress_time = datetime.now()
                self.consecutive_idle_checks = 0
                progress_made = True
            else:
                self.consecutive_idle_checks += 1
                idle_minutes = self.consecutive_idle_checks * (self.check_interval / 60)
                logger.info(f"⏸️  No progress for {idle_minutes:.0f} minutes (total: {total_records:,} records)")
                
            # Save status
            status = {
                'timestamp': datetime.now().isoformat(),
                'total_records': total_records,
                'table_counts': table_counts,
                'last_progress': self.last_progress_time.isoformat() if self.last_progress_time else None,
                'consecutive_idle_checks': self.consecutive_idle_checks,
                'guardian_status': 'active'
            }
            
            with open(self.status_file, 'w') as f:
                json.dump(status, f, indent=2)
                
            return progress_made
            
        except Exception as e:
            logger.error(f"❌ Failed to check status: {e}")
            return False
            
    def trigger_ingestion(self, mode='daily-update'):
        """Trigger the ingestion service"""
        try:
            token = self.get_auth_token()
            if not token:
                logger.error("❌ No auth token available")
                return False
                
            headers = {
                'Authorization': f'Bearer {token}',
                'Content-Type': 'application/json'
            }
            
            logger.info(f"🚀 Triggering {mode} ingestion...")
            
            response = requests.post(
                f"{self.service_url}/trigger",
                headers=headers,
                json={'mode': mode},
                timeout=300  # 5 minute timeout
            )
            
            if response.status_code == 200:
                logger.info(f"✅ Successfully triggered {mode} ingestion")
                self.consecutive_idle_checks = 0
                return True
            else:
                logger.error(f"❌ Failed to trigger ingestion: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Failed to trigger ingestion: {e}")
            return False
            
    def check_service_health(self):
        """Check if the service is healthy"""
        try:
            token = self.get_auth_token()
            if not token:
                return False
                
            headers = {'Authorization': f'Bearer {token}'}
            
            response = requests.get(
                f"{self.service_url}/status",
                headers=headers,
                timeout=30
            )
            
            return response.status_code == 200
            
        except Exception as e:
            logger.error(f"❌ Service health check failed: {e}")
            return False
            
    def run_forever(self):
        """Main guardian loop - runs indefinitely"""
        logger.info("🛡️  J-Quants Ingestion Guardian Started")
        logger.info(f"📊 Checking every {self.check_interval} seconds")
        logger.info(f"⏰ Will trigger ingestion after {self.max_idle_minutes} minutes of inactivity")
        logger.info("❌ Press Ctrl+C to stop")
        
        # Initial status check
        self.check_ingestion_status()
        
        while self.running:
            try:
                # Sleep first to give ingestion time to work
                logger.info(f"😴 Sleeping for {self.check_interval} seconds...")
                time.sleep(self.check_interval)
                
                # Check progress
                progress_made = self.check_ingestion_status()
                
                # If no progress for too long, trigger ingestion
                idle_minutes = self.consecutive_idle_checks * (self.check_interval / 60)
                if idle_minutes >= self.max_idle_minutes:
                    logger.warning(f"⚠️  No progress for {idle_minutes:.0f} minutes - triggering ingestion!")
                    
                    # Check service health first
                    if self.check_service_health():
                        # Try different modes in order of priority
                        modes = ['daily-update', 'dividends-only', 'prices-only', 'companies-only']
                        
                        for mode in modes:
                            if self.trigger_ingestion(mode):
                                logger.info(f"✅ Successfully triggered {mode} - waiting for progress...")
                                break
                            else:
                                logger.warning(f"⚠️  Failed to trigger {mode}, trying next mode...")
                                time.sleep(5)
                    else:
                        logger.error("❌ Service is not healthy - will retry later")
                        
            except KeyboardInterrupt:
                logger.info("🛑 Keyboard interrupt received")
                break
            except Exception as e:
                logger.error(f"❌ Unexpected error: {e}")
                logger.info("⏸️  Continuing after error...")
                time.sleep(30)  # Brief pause after error
                
        logger.info("👋 Guardian stopped")
        
if __name__ == "__main__":
    guardian = IngestionGuardian()
    guardian.run_forever()