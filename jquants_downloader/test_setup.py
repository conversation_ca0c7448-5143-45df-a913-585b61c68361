#!/usr/bin/env python3
"""Test script to verify the J-Quants pipeline setup."""

import os
import sys
from datetime import datetime

def test_imports():
    """Test if all required modules can be imported."""
    print("Testing imports...")
    
    try:
        import requests
        print("✓ requests")
    except ImportError as e:
        print(f"✗ requests: {e}")
        return False
    
    try:
        from google.cloud import bigquery
        print("✓ google-cloud-bigquery")
    except ImportError as e:
        print(f"✗ google-cloud-bigquery: {e}")
        return False
    
    try:
        import pandas as pd
        print("✓ pandas")
    except ImportError as e:
        print(f"✗ pandas: {e}")
        return False
    
    try:
        from pydantic import BaseModel
        print("✓ pydantic")
    except ImportError as e:
        print(f"✗ pydantic: {e}")
        return False
    
    try:
        from dotenv import load_dotenv
        print("✓ python-dotenv")
    except ImportError as e:
        print(f"✗ python-dotenv: {e}")
        return False
    
    try:
        from tenacity import retry
        print("✓ tenacity")
    except ImportError as e:
        print(f"✗ tenacity: {e}")
        return False
    
    return True


def test_local_imports():
    """Test if local modules can be imported."""
    print("\nTesting local imports...")
    
    try:
        from config import Config
        print("✓ config")
    except ImportError as e:
        print(f"✗ config: {e}")
        return False
    
    try:
        from models import ListedCompany, ListedCompaniesResponse
        print("✓ models")
    except ImportError as e:
        print(f"✗ models: {e}")
        return False
    
    try:
        from jquants_client import JQuantsClient
        print("✓ jquants_client")
    except ImportError as e:
        print(f"✗ jquants_client: {e}")
        return False
    
    try:
        from bigquery_client import BigQueryClient
        print("✓ bigquery_client")
    except ImportError as e:
        print(f"✗ bigquery_client: {e}")
        return False
    
    try:
        from data_pipeline import DataPipeline
        print("✓ data_pipeline")
    except ImportError as e:
        print(f"✗ data_pipeline: {e}")
        return False
    
    return True


def test_environment():
    """Test environment configuration."""
    print("\nTesting environment configuration...")
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    required_vars = [
        "JQUANTS_EMAIL",
        "JQUANTS_PASSWORD",
        "GOOGLE_CLOUD_PROJECT"
    ]
    
    optional_vars = [
        "JQUANTS_REFRESH_TOKEN",
        "GOOGLE_APPLICATION_CREDENTIALS",
        "BIGQUERY_DATASET",
        "BIGQUERY_LOCATION"
    ]
    
    all_good = True
    
    for var in required_vars:
        value = os.getenv(var)
        if value:
            print(f"✓ {var}: {'*' * min(len(value), 10)}")
        else:
            print(f"✗ {var}: Not set (REQUIRED)")
            all_good = False
    
    for var in optional_vars:
        value = os.getenv(var)
        if value:
            print(f"✓ {var}: {'*' * min(len(value), 10)}")
        else:
            print(f"○ {var}: Not set (optional)")
    
    return all_good


def test_gcp_credentials():
    """Test Google Cloud credentials."""
    print("\nTesting Google Cloud credentials...")
    
    try:
        from google.cloud import bigquery
        from google.auth import default
        
        # Try to get default credentials
        credentials, project = default()
        print(f"✓ Default credentials found for project: {project}")
        
        # Try to create BigQuery client
        client = bigquery.Client()
        print(f"✓ BigQuery client created successfully")
        
        return True
        
    except Exception as e:
        print(f"✗ Google Cloud credentials error: {e}")
        print("  Make sure GOOGLE_APPLICATION_CREDENTIALS is set correctly")
        return False


def test_config_validation():
    """Test configuration validation."""
    print("\nTesting configuration validation...")
    
    try:
        from config import Config
        Config.validate()
        print("✓ Configuration validation passed")
        return True
    except Exception as e:
        print(f"✗ Configuration validation failed: {e}")
        return False


def main():
    """Run all tests."""
    print("J-Quants Pipeline Setup Test")
    print("=" * 40)
    print(f"Test time: {datetime.now()}")
    print()
    
    tests = [
        ("Package Imports", test_imports),
        ("Local Module Imports", test_local_imports),
        ("Environment Variables", test_environment),
        ("GCP Credentials", test_gcp_credentials),
        ("Configuration Validation", test_config_validation),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name}: Exception occurred: {e}")
            results.append((test_name, False))
        print()
    
    # Summary
    print("=" * 40)
    print("TEST SUMMARY")
    print("=" * 40)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Your setup looks good.")
        print("\nNext steps:")
        print("1. Run: python main.py --setup-only")
        print("2. Run: python main.py")
        return 0
    else:
        print(f"\n❌ {total - passed} test(s) failed. Please fix the issues above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
