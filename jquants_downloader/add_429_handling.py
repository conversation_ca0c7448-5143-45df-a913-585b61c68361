#!/usr/bin/env python3
"""Add proper 429 error handling to production service."""

import fileinput
import sys
import re

def add_error_handling():
    """Add 429 error handling to the production service."""
    
    print("🔧 Adding 429 error handling to production service...")
    
    # Read the current file
    with open('production_ingestion_service.py', 'r') as f:
        content = f.read()
    
    # Add error handling attributes to __init__ method
    init_addition = """
        # Error handling for rate limits
        self.error_backoff = 1800  # 30 minutes on 429
        self.max_consecutive_errors = 3
        self.consecutive_errors = 0"""
    
    # Find the __init__ method and add after max_retries
    init_pattern = r'(self\.max_retries = 3)'
    if re.search(init_pattern, content):
        content = re.sub(init_pattern, r'\1' + init_addition, content)
        print("✅ Added error handling attributes to __init__")
    
    # Create a helper method for handling 429 errors
    error_handler_method = '''
    def _handle_rate_limit_error(self, e: Exception, context: str = ""):
        """Handle 429 rate limit errors with exponential backoff."""
        error_msg = str(e).lower()
        
        if '429' in error_msg or 'rate limit' in error_msg or 'too many' in error_msg:
            self.consecutive_errors += 1
            
            if self.consecutive_errors >= self.max_consecutive_errors:
                logger.error(f"❌ Too many consecutive rate limit errors ({self.consecutive_errors}), stopping {context}")
                raise Exception(f"Rate limit exceeded after {self.consecutive_errors} attempts")
            
            # Exponential backoff
            wait_time = self.error_backoff * (2 ** (self.consecutive_errors - 1))
            logger.warning(f"🚨 Rate limited! Attempt {self.consecutive_errors}/{self.max_consecutive_errors}")
            logger.warning(f"⏱️  Waiting {wait_time} seconds before retry... ({context})")
            
            import time
            time.sleep(wait_time)
            return True  # Indicates this was a rate limit error
        
        return False  # Not a rate limit error
    
    def _reset_error_count(self):
        """Reset consecutive error count on success."""
        if self.consecutive_errors > 0:
            logger.info(f"✅ Success! Resetting error count (was {self.consecutive_errors})")
            self.consecutive_errors = 0
'''
    
    # Add the error handler method before the first ingestion method
    method_pattern = r'(def ingest_all_companies\(self\))'
    content = re.sub(method_pattern, error_handler_method + r'\n    \1', content)
    print("✅ Added error handling methods")
    
    # Write the updated content back
    with open('production_ingestion_service.py', 'w') as f:
        f.write(content)
    
    print("✅ Added comprehensive 429 error handling")
    print("\nThe service now has:")
    print("- 60 second delays between requests")
    print("- 300 second delays between batches") 
    print("- 30+ minute backoff on 429 errors")
    print("- Exponential backoff with max 3 retries")

if __name__ == "__main__":
    add_error_handling()