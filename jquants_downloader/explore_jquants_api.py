#!/usr/bin/env python3
"""Explore available J-Quants API endpoints."""

from jquantsapi import Client
from dotenv import load_dotenv
import os
import inspect

def explore_jquants_api():
    """Explore available J-Quants API methods."""
    load_dotenv()
    
    email = os.getenv("JQUANTS_EMAIL", "").strip('"\'')
    password = os.getenv("JQUANTS_PASSWORD", "").strip('"\'')
    
    print("J-Quants API Explorer")
    print("=" * 50)
    
    try:
        # Create client
        client = Client(mail_address=email, password=password)
        print("✅ Client created successfully")
        
        # Get all methods that start with 'get_'
        methods = [method for method in dir(client) if method.startswith('get_') and not method.startswith('_')]
        
        print(f"\n📋 Available API Methods ({len(methods)}):")
        print("-" * 50)
        
        for i, method in enumerate(methods, 1):
            method_obj = getattr(client, method)
            if callable(method_obj):
                # Get method signature
                try:
                    sig = inspect.signature(method_obj)
                    print(f"{i:2d}. {method}{sig}")
                except:
                    print(f"{i:2d}. {method}()")
        
        print("\n🔍 Testing a few key methods:")
        print("-" * 50)
        
        # Test get_listed_info
        try:
            print("Testing get_listed_info()...")
            companies = client.get_listed_info()
            print(f"✅ get_listed_info(): {len(companies)} companies")
            if len(companies) > 0:
                print(f"   Columns: {list(companies.columns)}")
        except Exception as e:
            print(f"❌ get_listed_info(): {e}")
        
        # Test other methods if they exist
        test_methods = [
            'get_prices_daily_quotes',
            'get_statements',
            'get_dividend',
            'get_fins_statements',
            'get_fins_announcement'
        ]
        
        for method_name in test_methods:
            if hasattr(client, method_name):
                try:
                    print(f"Testing {method_name}()...")
                    method = getattr(client, method_name)
                    # Try with minimal parameters
                    result = method(code="", date_yyyymmdd="")
                    print(f"✅ {method_name}(): {len(result)} records")
                    if len(result) > 0:
                        print(f"   Columns: {list(result.columns)}")
                except Exception as e:
                    print(f"❌ {method_name}(): {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    explore_jquants_api()
