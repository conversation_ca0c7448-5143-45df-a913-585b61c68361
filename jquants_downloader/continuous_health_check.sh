#!/bin/bash
# ABOUTME: Continuous health check script that monitors ingestion and alerts on issues
# ABOUTME: Runs every 5 minutes to ensure ingestion is progressing smoothly

echo "🚀 Starting continuous health check for J-Quants ingestion"
echo "   Checking every 5 minutes..."
echo "   Press Ctrl+C to stop"
echo ""

while true; do
    echo "================================================================================"
    echo "Health Check at $(date '+%Y-%m-%d %H:%M:%S')"
    echo "================================================================================"
    
    # Check if monitor is running
    if [ -f "monitor.pid" ]; then
        PID=$(cat monitor.pid)
        if ps -p $PID > /dev/null 2>&1; then
            echo "✅ Background monitor is running (PID: $PID)"
        else
            echo "❌ Background monitor has stopped! Restarting..."
            rm -f monitor.pid
            nohup python background_monitor.py > /dev/null 2>&1 &
            echo "   Restarted with PID: $!"
        fi
    else
        echo "❌ Background monitor is not running! Starting..."
        nohup python background_monitor.py > /dev/null 2>&1 &
        echo "   Started with PID: $!"
    fi
    
    # Check status file age
    if [ -f "ingestion_status.json" ]; then
        # Get file modification time in seconds
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS
            FILE_AGE=$(($(date +%s) - $(stat -f %m ingestion_status.json)))
        else
            # Linux
            FILE_AGE=$(($(date +%s) - $(stat -c %Y ingestion_status.json)))
        fi
        
        AGE_MINUTES=$((FILE_AGE / 60))
        
        if [ $AGE_MINUTES -gt 5 ]; then
            echo "⚠️  WARNING: Status file is $AGE_MINUTES minutes old (stale)"
        else
            echo "✅ Status file is fresh ($AGE_MINUTES minutes old)"
        fi
        
        # Check for recent progress
        NEW_RECORDS=$(cat ingestion_status.json | jq -r '.new_records_this_check // 0')
        if [ "$NEW_RECORDS" -gt 0 ]; then
            echo "✅ Active ingestion: $NEW_RECORDS new records in last check"
        else
            echo "⏸️  No new records in last check"
        fi
        
        # Check for errors
        ERROR_COUNT=$(cat ingestion_status.json | jq -r '.recent_errors | length')
        if [ "$ERROR_COUNT" -gt 0 ]; then
            echo "⚠️  $ERROR_COUNT recent errors detected!"
            cat ingestion_status.json | jq -r '.recent_errors[:2][] | "   - \(.message[:80])"'
        fi
    else
        echo "❌ No status file found"
    fi
    
    # Check Cloud Run service health
    echo ""
    echo "Checking Cloud Run service..."
    TOKEN=$(gcloud auth print-identity-token 2>/dev/null)
    if [ -n "$TOKEN" ]; then
        HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" \
            -H "Authorization: Bearer $TOKEN" \
            https://jquants-ingestion-621634133093.asia-northeast1.run.app/)
        
        if [ "$HTTP_CODE" = "200" ]; then
            echo "✅ Cloud Run service is healthy"
        else
            echo "❌ Cloud Run service returned: $HTTP_CODE"
        fi
    else
        echo "⚠️  Could not get auth token"
    fi
    
    echo ""
    echo "Next check in 5 minutes..."
    echo ""
    
    # Sleep for 5 minutes
    sleep 300
done