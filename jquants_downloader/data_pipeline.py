"""Main data pipeline for J-Quants to BigQuery ingestion."""

import logging
from datetime import datetime, date
from typing import Optional, List
import sys

from config import Config
from jquants_client import JQuantsClient, JQuantsAPIError
from bigquery_client import BigQueryClient
from models import ListedCompany

# Set up logging
logging.basicConfig(
    level=getattr(logging, Config.LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('jquants_pipeline.log')
    ]
)

logger = logging.getLogger(__name__)


class DataPipeline:
    """Main data pipeline for ingesting J-Quants data into BigQuery."""

    def __init__(self):
        """Initialize the data pipeline."""
        # Validate configuration
        Config.validate()

        # Initialize clients
        self.jquants_client = JQuantsClient(
            email=Config.JQUANTS_EMAIL,
            password=Config.JQUANTS_PASSWORD,
            refresh_token=Config.JQUANTS_REFRESH_TOKEN
        )

        self.bigquery_client = BigQueryClient(
            project_id=Config.GOOGLE_CLOUD_PROJECT,
            dataset_id=Config.BIGQUERY_DATASET,
            location=Config.BIGQUERY_LOCATION
        )

        logger.info("Data pipeline initialized")

    def setup_bigquery(self) -> None:
        """Set up BigQuery dataset and tables."""
        logger.info("Setting up BigQuery infrastructure")

        try:
            # Create dataset
            self.bigquery_client.create_dataset()

            # Create companies table
            self.bigquery_client.create_companies_table()

            logger.info("BigQuery setup completed successfully")

        except Exception as e:
            logger.error(f"Error setting up BigQuery: {e}")
            raise

    def ingest_companies_data(self, target_date: Optional[str] = None, replace_existing: bool = False) -> int:
        """Ingest companies data from J-Quants API.

        Args:
            target_date: Specific date to fetch data for (YYYY-MM-DD). If None, gets current data.
            replace_existing: Whether to replace existing data for the date

        Returns:
            Number of companies ingested
        """
        logger.info(f"Starting companies data ingestion (date={target_date}, replace={replace_existing})")

        try:
            # Fetch companies data from J-Quants
            companies = self.jquants_client.get_listed_companies(date=target_date)

            if not companies:
                logger.warning("No companies data retrieved from J-Quants API")
                return 0

            logger.info(f"Retrieved {len(companies)} companies from J-Quants API")

            # If replacing existing data, delete old data first
            if replace_existing and target_date:
                deleted_count = self.bigquery_client.delete_companies_by_date(target_date)
                logger.info(f"Deleted {deleted_count} existing records for date {target_date}")

            # Insert data into BigQuery
            write_disposition = "WRITE_TRUNCATE" if replace_existing and not target_date else "WRITE_APPEND"
            self.bigquery_client.insert_companies_data(companies, write_disposition=write_disposition)

            logger.info(f"Successfully ingested {len(companies)} companies into BigQuery")
            return len(companies)

        except JQuantsAPIError as e:
            logger.error(f"J-Quants API error during ingestion: {e}")
            raise
        except Exception as e:
            logger.error(f"Error during companies data ingestion: {e}")
            raise

    def validate_data_quality(self) -> dict:
        """Validate the quality of ingested data.

        Returns:
            Dictionary with validation results
        """
        logger.info("Starting data quality validation")

        try:
            # Get basic statistics
            total_companies = self.bigquery_client.get_companies_count()
            latest_companies = self.bigquery_client.get_latest_companies_by_date(limit=5)

            # Basic validation checks
            validation_results = {
                "total_companies": total_companies,
                "has_data": total_companies > 0,
                "latest_records_count": len(latest_companies),
                "latest_records": latest_companies,
                "validation_timestamp": datetime.now().isoformat(),
            }

            # Check for required fields in latest records
            if latest_companies:
                sample_record = latest_companies[0]
                required_fields = ["date", "code", "company_name"]
                missing_fields = [field for field in required_fields if not sample_record.get(field)]

                validation_results["required_fields_present"] = len(missing_fields) == 0
                validation_results["missing_fields"] = missing_fields
            else:
                validation_results["required_fields_present"] = False
                validation_results["missing_fields"] = ["No data available"]

            # Overall validation status
            validation_results["validation_passed"] = (
                validation_results["has_data"] and
                validation_results["required_fields_present"]
            )

            logger.info(f"Data quality validation completed. Status: {'PASSED' if validation_results['validation_passed'] else 'FAILED'}")
            return validation_results

        except Exception as e:
            logger.error(f"Error during data quality validation: {e}")
            return {
                "validation_passed": False,
                "error": str(e),
                "validation_timestamp": datetime.now().isoformat(),
            }

    def run_full_pipeline(self, target_date: Optional[str] = None, replace_existing: bool = False) -> dict:
        """Run the complete data pipeline.

        Args:
            target_date: Specific date to fetch data for (YYYY-MM-DD)
            replace_existing: Whether to replace existing data

        Returns:
            Dictionary with pipeline execution results
        """
        pipeline_start = datetime.now()
        logger.info("Starting full data pipeline execution")

        try:
            # Step 1: Setup BigQuery
            self.setup_bigquery()

            # Step 2: Ingest companies data
            companies_count = self.ingest_companies_data(target_date, replace_existing)

            # Step 3: Validate data quality
            validation_results = self.validate_data_quality()

            pipeline_end = datetime.now()
            execution_time = (pipeline_end - pipeline_start).total_seconds()

            results = {
                "pipeline_status": "SUCCESS" if validation_results.get("validation_passed") else "FAILED",
                "execution_time_seconds": execution_time,
                "companies_ingested": companies_count,
                "validation_results": validation_results,
                "start_time": pipeline_start.isoformat(),
                "end_time": pipeline_end.isoformat(),
            }

            logger.info(f"Pipeline execution completed. Status: {results['pipeline_status']}, Time: {execution_time:.2f}s")
            return results

        except Exception as e:
            pipeline_end = datetime.now()
            execution_time = (pipeline_end - pipeline_start).total_seconds()

            logger.error(f"Pipeline execution failed: {e}")

            return {
                "pipeline_status": "ERROR",
                "execution_time_seconds": execution_time,
                "error": str(e),
                "start_time": pipeline_start.isoformat(),
                "end_time": pipeline_end.isoformat(),
            }
