openapi: 3.0.3
info:
  title: J-Quants API
  description: |
    J-Quants API provides comprehensive Japanese stock market data including:
    - Stock prices (OHLC) with morning/afternoon session data
    - Listed company information
    - Financial statements and earnings data
    - Market trading data by investor type
    - Margin trading and short selling data
    - Index data (TOPIX, Nikkei, etc.)
    - Derivatives data (futures and options)
    - Dividend information
    - Trading calendar

    ## Authentication
    The API uses Bearer token authentication. You need to:
    1. Get a refresh token using your email/password
    2. Exchange the refresh token for an ID token
    3. Use the ID token in the Authorization header

    ## Rate Limits
    API has rate limits. If usage exceeds certain thresholds, access will be temporarily restricted.

    ## Pagination
    Large responses include a `pagination_key` field. Use this key in subsequent requests to get more data.

    ## Data Compression
    Responses are Gzip compressed by default to reduce data traffic.

  version: 1.0.0
  contact:
    name: J-Quants API Support
    url: https://jpx.gitbook.io/j-quants-en/
  license:
    name: J-Quants Terms of Service
    url: https://jpx.gitbook.io/j-quants-en/

servers:
  - url: https://api.jquants.com/v1
    description: Production server

security:
  - BearerAuth: []

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: Use the ID token obtained from /token/auth_refresh endpoint

  schemas:
    Error:
      type: object
      properties:
        message:
          type: string
          description: Error message
      required:
        - message

    PaginationResponse:
      type: object
      properties:
        pagination_key:
          type: string
          description: Key for retrieving next page of results

    RefreshTokenRequest:
      type: object
      properties:
        mailaddress:
          type: string
          format: email
          description: Email address
        password:
          type: string
          description: Password
      required:
        - mailaddress
        - password

    RefreshTokenResponse:
      type: object
      properties:
        refreshToken:
          type: string
          description: Refresh token valid for one week
      required:
        - refreshToken

    ListedInfo:
      type: object
      properties:
        Date:
          type: string
          format: date
          description: Date of application of information (YYYY-MM-DD)
        Code:
          type: string
          description: Issue code
        CompanyName:
          type: string
          description: Company Name (Japanese)
        CompanyNameEnglish:
          type: string
          description: Company Name (English)
        Sector17Code:
          type: string
          description: 17-Sector code
        Sector17CodeName:
          type: string
          description: 17-Sector code name (Japanese)
        Sector33Code:
          type: string
          description: 33-Sector code
        Sector33CodeName:
          type: string
          description: 33-Sector code name (Japanese)
        ScaleCategory:
          type: string
          description: TOPIX Scale category
        MarketCode:
          type: string
          description: Market segment code
        MarketCodeName:
          type: string
          description: Market segment code name (Japanese)
        MarginCode:
          type: string
          description: Flags of margin and loan issues (1=Margin, 2=Loan, 3=Other)
        MarginCodeName:
          type: string
          description: Name of flags of margin and loan issues

    ListedInfoResponse:
      allOf:
        - $ref: '#/components/schemas/PaginationResponse'
        - type: object
          properties:
            info:
              type: array
              items:
                $ref: '#/components/schemas/ListedInfo'

    DailyQuote:
      type: object
      properties:
        Date:
          type: string
          format: date
          description: Date (YYYY-MM-DD)
        Code:
          type: string
          description: Issue code
        Open:
          type: number
          nullable: true
          description: Open Price (before adjustment)
        High:
          type: number
          nullable: true
          description: High price (before adjustment)
        Low:
          type: number
          nullable: true
          description: Low price (before adjustment)
        Close:
          type: number
          nullable: true
          description: Close price (before adjustment)
        UpperLimit:
          type: string
          description: Flag of hitting the upper price limit (0=No, 1=Yes)
        LowerLimit:
          type: string
          description: Flag of hitting the lower price limit (0=No, 1=Yes)
        Volume:
          type: number
          nullable: true
          description: Trading volume (before Adjustment)
        TurnoverValue:
          type: number
          nullable: true
          description: Trading value
        AdjustmentFactor:
          type: number
          description: Adjustment factor
        AdjustmentOpen:
          type: number
          nullable: true
          description: Adjusted open price
        AdjustmentHigh:
          type: number
          nullable: true
          description: Adjusted high price
        AdjustmentLow:
          type: number
          nullable: true
          description: Adjusted low price
        AdjustmentClose:
          type: number
          nullable: true
          description: Adjusted close price
        AdjustmentVolume:
          type: number
          nullable: true
          description: Adjusted volume
        # Morning session data (Premium plan only)
        MorningOpen:
          type: number
          nullable: true
          description: Open price of the morning session (Premium plan only)
        MorningHigh:
          type: number
          nullable: true
          description: High price of the morning session (Premium plan only)
        MorningLow:
          type: number
          nullable: true
          description: Low price of the morning session (Premium plan only)
        MorningClose:
          type: number
          nullable: true
          description: Close price of the morning session (Premium plan only)
        MorningUpperLimit:
          type: string
          description: Flag of hitting the upper price limit in morning session (Premium plan only)
        MorningLowerLimit:
          type: string
          description: Flag of hitting the lower price limit in morning session (Premium plan only)
        MorningVolume:
          type: number
          nullable: true
          description: Trading volume of the morning session (Premium plan only)
        MorningTurnoverValue:
          type: number
          nullable: true
          description: Trading value of the morning session (Premium plan only)
        MorningAdjustmentOpen:
          type: number
          nullable: true
          description: Adjusted open price of the morning session (Premium plan only)
        MorningAdjustmentHigh:
          type: number
          nullable: true
          description: Adjusted high price of the morning session (Premium plan only)
        MorningAdjustmentLow:
          type: number
          nullable: true
          description: Adjusted low price of the morning session (Premium plan only)
        MorningAdjustmentClose:
          type: number
          nullable: true
          description: Adjusted close price of the morning session (Premium plan only)
        MorningAdjustmentVolume:
          type: number
          nullable: true
          description: Adjusted trading volume of the morning session (Premium plan only)
        # Afternoon session data (Premium plan only)
        AfternoonOpen:
          type: number
          nullable: true
          description: Open price of the afternoon session (Premium plan only)
        AfternoonHigh:
          type: number
          nullable: true
          description: High price of the afternoon session (Premium plan only)
        AfternoonLow:
          type: number
          nullable: true
          description: Low price of the afternoon session (Premium plan only)
        AfternoonClose:
          type: number
          nullable: true
          description: Close price of the afternoon session (Premium plan only)
        AfternoonUpperLimit:
          type: string
          description: Flag of hitting the upper price limit in afternoon session (Premium plan only)
        AfternoonLowerLimit:
          type: string
          description: Flag of hitting the lower price limit in afternoon session (Premium plan only)
        AfternoonVolume:
          type: number
          nullable: true
          description: Trading volume of the afternoon session (Premium plan only)
        AfternoonTurnoverValue:
          type: number
          nullable: true
          description: Trading value of the afternoon session (Premium plan only)
        AfternoonAdjustmentOpen:
          type: number
          nullable: true
          description: Adjusted open price of the afternoon session (Premium plan only)
        AfternoonAdjustmentHigh:
          type: number
          nullable: true
          description: Adjusted high price of the afternoon session (Premium plan only)
        AfternoonAdjustmentLow:
          type: number
          nullable: true
          description: Adjusted low price of the afternoon session (Premium plan only)
        AfternoonAdjustmentClose:
          type: number
          nullable: true
          description: Adjusted close price of the afternoon session (Premium plan only)
        AfternoonAdjustmentVolume:
          type: number
          nullable: true
          description: Adjusted trading volume of the afternoon session (Premium plan only)

    DailyQuotesResponse:
      allOf:
        - $ref: '#/components/schemas/PaginationResponse'
        - type: object
          properties:
            daily_quotes:
              type: array
              items:
                $ref: '#/components/schemas/DailyQuote'

    FinancialStatement:
      type: object
      properties:
        DisclosedDate:
          type: string
          format: date
          description: Disclosed Date
        DisclosedTime:
          type: string
          description: Disclosed Time (HH:MM:SS)
        LocalCode:
          type: string
          description: Issue Code (5-character)
        DisclosureNumber:
          type: string
          description: Disclosure Number
        TypeOfDocument:
          type: string
          description: Type of document
        TypeOfCurrentPeriod:
          type: string
          description: Type of current accounting period (1Q, 2Q, 3Q, 4Q, 5Q, FY)
        CurrentPeriodStartDate:
          type: string
          format: date
          description: Start date of current accounting period
        CurrentPeriodEndDate:
          type: string
          format: date
          description: End date of current accounting period
        CurrentFiscalYearStartDate:
          type: string
          format: date
          description: Start date of current fiscal year
        CurrentFiscalYearEndDate:
          type: string
          format: date
          description: End date of current fiscal year
        NextFiscalYearStartDate:
          type: string
          format: date
          description: Start date of next fiscal year
        NextFiscalYearEndDate:
          type: string
          format: date
          description: End date of next fiscal year
        NetSales:
          type: string
          description: Net Sales
        OperatingProfit:
          type: string
          description: Operating Profit
        OrdinaryProfit:
          type: string
          description: Ordinary Profit
        Profit:
          type: string
          description: Profit
        EarningsPerShare:
          type: string
          description: Earnings per share
        DilutedEarningsPerShare:
          type: string
          description: Diluted Earnings per share
        TotalAssets:
          type: string
          description: Total assets
        Equity:
          type: string
          description: Equity
        EquityToAssetRatio:
          type: string
          description: Equity to asset ratio
        BookValuePerShare:
          type: string
          description: Book value per share
        CashFlowsFromOperatingActivities:
          type: string
          description: Cash flows from operating activities
        CashFlowsFromInvestingActivities:
          type: string
          description: Cash flows from investing activities
        CashFlowsFromFinancingActivities:
          type: string
          description: Cash flows from financing activities
        CashAndEquivalents:
          type: string
          description: Cash and equivalents

    FinancialStatementsResponse:
      allOf:
        - $ref: '#/components/schemas/PaginationResponse'
        - type: object
          properties:
            statements:
              type: array
              items:
                $ref: '#/components/schemas/FinancialStatement'

    Dividend:
      type: object
      properties:
        AnnouncementDate:
          type: string
          format: date
          description: Announcement Date (YYYY-MM-DD)
        AnnouncementTime:
          type: string
          description: Announcement Time (HH:MM)
        Code:
          type: string
          description: Issue code
        ReferenceNumber:
          type: string
          description: Reference number
        StatusCode:
          type: string
          description: Code stands for update status (1=new, 2=revised, 3=delete)
        BoardMeetingDate:
          type: string
          format: date
          description: Date of Board of Directors' resolution
        InterimFinalCode:
          type: string
          description: Code stands for interim/final dividend (1=interim, 2=final)
        ForecastResultCode:
          type: string
          description: Code stands for determined/forecast (1=result, 2=forecast)
        InterimFinalTerm:
          type: string
          description: Interim Final Term
        GrossDividendRate:
          type: string
          description: Dividend value per share
        RecordDate:
          type: string
          format: date
          description: Record date
        ExDate:
          type: string
          format: date
          description: Ex-rights date
        ActualRecordDate:
          type: string
          format: date
          description: Date of Dividend Vesting
        PayableDate:
          type: string
          format: date
          description: Scheduled payment start date
        CAReferenceNumber:
          type: string
          description: CA Reference number
        DistributionAmount:
          type: string
          description: Amount of cash delivered per share
        RetainedEarnings:
          type: string
          description: Retained earnings per share
        DeemedDividend:
          type: string
          description: Deemed dividend per share
        DeemedCapitalGains:
          type: string
          description: Amount of deemed transfer income per share
        NetAssetDecreaseRatio:
          type: string
          description: Decrease ratio in net assets
        CommemorativeSpecialCode:
          type: string
          description: Code stands for Commemorative/Special dividend (0=Normal, 1=Commemorative, 2=Special, 3=Both)
        CommemorativeDividendRate:
          type: string
          description: Commemorative dividend value per share
        SpecialDividendRate:
          type: string
          description: Special dividend value per share

    DividendResponse:
      allOf:
        - $ref: '#/components/schemas/PaginationResponse'
        - type: object
          properties:
            dividend:
              type: array
              items:
                $ref: '#/components/schemas/Dividend'

    TopixData:
      type: object
      properties:
        Date:
          type: string
          format: date
          description: Date (YYYY-MM-DD)
        Open:
          type: number
          description: Open Price
        High:
          type: number
          description: High Price
        Low:
          type: number
          description: Low price
        Close:
          type: number
          description: Close price

    TopixResponse:
      allOf:
        - $ref: '#/components/schemas/PaginationResponse'
        - type: object
          properties:
            topix:
              type: array
              items:
                $ref: '#/components/schemas/TopixData'

    IndexOption:
      type: object
      properties:
        Date:
          type: string
          format: date
          description: Trading day (YYYY-MM-DD)
        Code:
          type: string
          description: Issue code
        WholeDayOpen:
          type: number
          description: Open price (whole day)
        WholeDayHigh:
          type: number
          description: High price (whole day)
        WholeDayLow:
          type: number
          description: Low price (whole day)
        WholeDayClose:
          type: number
          description: Close price (whole day)
        NightSessionOpen:
          type: number
          nullable: true
          description: Open price (night session)
        NightSessionHigh:
          type: number
          nullable: true
          description: High price (night session)
        NightSessionLow:
          type: number
          nullable: true
          description: Low price (night session)
        NightSessionClose:
          type: number
          nullable: true
          description: Close price (night session)
        DaySessionOpen:
          type: number
          description: Open price (day session)
        DaySessionHigh:
          type: number
          description: High price (day session)
        DaySessionLow:
          type: number
          description: Low price (day session)
        DaySessionClose:
          type: number
          description: Close price (day session)
        Volume:
          type: number
          description: Volume
        OpenInterest:
          type: number
          description: Open interest
        TurnoverValue:
          type: number
          description: Trading value
        ContractMonth:
          type: string
          description: Contract month (YYYY-MM)
        StrikePrice:
          type: number
          description: Strike price
        "Volume(OnlyAuction)":
          type: number
          description: Volume (only auction)
        EmergencyMarginTriggerDivision:
          type: string
          description: Emergency margin trigger division (001=triggered, 002=settlement price calculated)
        PutCallDivision:
          type: string
          description: Put Call division (1=Put, 2=Call)
        LastTradingDay:
          type: string
          format: date
          description: Last trading day (YYYY-MM-DD)
        SpecialQuotationDay:
          type: string
          format: date
          description: Special quotation day (YYYY-MM-DD)
        SettlementPrice:
          type: number
          description: Settlement price
        TheoreticalPrice:
          type: number
          description: Theoretical price
        BaseVolatility:
          type: number
          description: Base volatility
        UnderlyingPrice:
          type: number
          description: Underlying asset price
        ImpliedVolatility:
          type: number
          description: Implied volatility
        InterestRate:
          type: number
          description: Interest rate for theoretical price calculation

    IndexOptionResponse:
      allOf:
        - $ref: '#/components/schemas/PaginationResponse'
        - type: object
          properties:
            index_option:
              type: array
              items:
                $ref: '#/components/schemas/IndexOption'

paths:
  /token/auth_user:
    post:
      tags:
        - Authentication
      summary: Get refresh token
      description: |
        Retrieve a refresh token using email and password.
        The refresh token is valid for one week.
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RefreshTokenRequest'
            example:
              mailaddress: "<EMAIL>"
              password: "your-password"
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RefreshTokenResponse'
              example:
                refreshToken: "your-refresh-token-here"
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                message: "'mailaddress' or 'password' is incorrect."
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                message: "Missing Authentication Token. The method or resources may not be supported."
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                message: "Unexpected error. Please try again later."

  /token/auth_refresh:
    post:
      tags:
        - Authentication
      summary: Get ID token
      description: |
        Exchange refresh token for an ID token.
        Use the ID token for API authentication.
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                refreshtoken:
                  type: string
                  description: Refresh token obtained from /token/auth_user
              required:
                - refreshtoken
            example:
              refreshtoken: "your-refresh-token-here"
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  idToken:
                    type: string
                    description: ID token for API authentication
                required:
                  - idToken
              example:
                idToken: "your-id-token-here"
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /listed/info:
    get:
      tags:
        - Listed Companies
      summary: Get listed issue information
      description: |
        Retrieve listed issue information as of the past, current day, and next business day.
        MarginCode and MarginCodeName are available for Standard and Premium plan users.
      parameters:
        - name: code
          in: query
          description: Issue code (e.g. 27800 or 2780). If 4-character code is specified, only common stock data is returned.
          schema:
            type: string
          example: "86970"
        - name: date
          in: query
          description: Date of application of information (e.g. 20210907 or 2021-09-07)
          schema:
            type: string
          example: "2023-03-24"
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ListedInfoResponse'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '413':
          description: Payload Too Large
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                message: "Response data is too large. Specify parameters to reduce the acquired data range."
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /prices/daily_quotes:
    get:
      tags:
        - Stock Prices
      summary: Get daily stock prices (OHLC)
      description: |
        Retrieve stock price information including OHLC data, volume, and trading value.
        Stock prices include both before and after adjustment for stock splits.
        Morning/afternoon session data is available only for Premium plan users.
      parameters:
        - name: code
          in: query
          description: Issue code (e.g. 27800 or 2780). If 4-character code is specified, only common stock data is returned.
          schema:
            type: string
          example: "86970"
        - name: from
          in: query
          description: Starting point of data period (e.g. 20210901 or 2021-09-01)
          schema:
            type: string
          example: "2023-03-01"
        - name: to
          in: query
          description: End point of data period (e.g. 20210907 or 2021-09-07)
          schema:
            type: string
          example: "2023-03-31"
        - name: date
          in: query
          description: Date of data (e.g. 20210907 or 2021-09-07). Required when from/to are not specified.
          schema:
            type: string
          example: "2023-03-24"
        - name: pagination_key
          in: query
          description: Pagination key from previous response
          schema:
            type: string
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DailyQuotesResponse'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '413':
          description: Payload Too Large
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /fins/statements:
    get:
      tags:
        - Financial Data
      summary: Get financial statements
      description: |
        Retrieve quarterly earnings summaries and disclosure information for listed companies.
        Either 'code' or 'date' must be specified.
      parameters:
        - name: code
          in: query
          description: Issue code (e.g. 27890 or 2789). If 4-character code is specified, only common stock data is returned.
          schema:
            type: string
          example: "86970"
        - name: date
          in: query
          description: Disclosure date (e.g. 20210901 or 2021-09-01)
          schema:
            type: string
          example: "2023-01-30"
        - name: pagination_key
          in: query
          description: Pagination key from previous response
          schema:
            type: string
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FinancialStatementsResponse'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                message: "This API requires at least 1 parameter as follows; 'date','code'."
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '413':
          description: Payload Too Large
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /fins/dividend:
    get:
      tags:
        - Financial Data
      summary: Get dividend information
      description: |
        Retrieve dividend information (determined and forecast) per share of listed companies,
        including record date, ex-rights date, and payable date.
        Either 'code' or 'date' must be specified.
      parameters:
        - name: code
          in: query
          description: Issue code (e.g. 27800 or 2780). If 4-character code is specified, only common stock data is returned.
          schema:
            type: string
          example: "86970"
        - name: from
          in: query
          description: Starting point of data period (e.g. 20210901 or 2021-09-01)
          schema:
            type: string
          example: "2023-01-01"
        - name: to
          in: query
          description: End point of data period (e.g. 20210907 or 2021-09-07)
          schema:
            type: string
          example: "2023-12-31"
        - name: date
          in: query
          description: Date (e.g. 20210907 or 2021-09-07). Required when from/to are not specified.
          schema:
            type: string
          example: "2023-03-24"
        - name: pagination_key
          in: query
          description: Pagination key from previous response
          schema:
            type: string
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DividendResponse'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '413':
          description: Payload Too Large
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /indices/topix:
    get:
      tags:
        - Indices
      summary: Get TOPIX prices (OHLC)
      description: |
        Retrieve TOPIX (Tokyo Stock Price Index) OHLC data.
        If from/to are not specified, all historical data is returned.
      parameters:
        - name: from
          in: query
          description: Starting point of data period (e.g. 20210901 or 2021-09-01)
          schema:
            type: string
          example: "2023-01-01"
        - name: to
          in: query
          description: End point of data period (e.g. 20210907 or 2021-09-07)
          schema:
            type: string
          example: "2023-12-31"
        - name: pagination_key
          in: query
          description: Pagination key from previous response
          schema:
            type: string
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TopixResponse'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                message: "'from' must be older than 'to'"
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '413':
          description: Payload Too Large
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /option/index_option:
    get:
      tags:
        - Derivatives
      summary: Get index option prices (OHLC)
      description: |
        Retrieve OHLC, settlement price, and theoretical price of Nikkei 225 Options.
        Data is only for Nikkei 225 Index Options (excluding Weekly Options and Flexible options).
        Date parameter is required.
      parameters:
        - name: date
          in: query
          required: true
          description: Date of data (e.g. 20210901 or 2021-09-01)
          schema:
            type: string
          example: "2023-03-24"
        - name: pagination_key
          in: query
          description: Pagination key from previous response
          schema:
            type: string
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IndexOptionResponse'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '413':
          description: Payload Too Large
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /markets/trades_spec:
    get:
      tags:
        - Market Data
      summary: Get trading by type of investors
      description: |
        Retrieve trading data categorized by investor type.
        Available for Standard and Premium plan users.
      parameters:
        - name: section
          in: query
          description: Market section (e.g. TSEPrime, TSEStandard, TSEGrowth)
          schema:
            type: string
          example: "TSEPrime"
        - name: from
          in: query
          description: Starting point of data period (e.g. 20210901 or 2021-09-01)
          schema:
            type: string
          example: "2023-01-01"
        - name: to
          in: query
          description: End point of data period (e.g. 20210907 or 2021-09-07)
          schema:
            type: string
          example: "2023-12-31"
        - name: pagination_key
          in: query
          description: Pagination key from previous response
          schema:
            type: string
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  trades_spec:
                    type: array
                    items:
                      type: object
                      properties:
                        Date:
                          type: string
                          format: date
                          description: Trade date
                        Section:
                          type: string
                          description: Market section
                        ProprietaryTotal:
                          type: number
                          description: Proprietary trading total
                        IndividualTotal:
                          type: number
                          description: Individual investor total
                        ForeignerTotal:
                          type: number
                          description: Foreign investor total
                        SecuritiesCompaniesTotal:
                          type: number
                          description: Securities companies total
                        InvestmentTrustsTotal:
                          type: number
                          description: Investment trusts total
                        BusinessCompaniesTotal:
                          type: number
                          description: Business companies total
                        OtherCorporationsTotal:
                          type: number
                          description: Other corporations total
                        InsuranceCompaniesTotal:
                          type: number
                          description: Insurance companies total
                        CityBanksRegionalBanksEtcTotal:
                          type: number
                          description: Banks total
                        TrustBanksTotal:
                          type: number
                          description: Trust banks total
                        OtherFinancialInstitutionsTotal:
                          type: number
                          description: Other financial institutions total
                        PensionFundsTotal:
                          type: number
                          description: Pension funds total
                  pagination_key:
                    type: string
                    description: Key for retrieving next page of results
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '413':
          description: Payload Too Large
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /markets/breakdown:
    get:
      tags:
        - Market Data
      summary: Get breakdown trading data
      description: |
        Retrieve breakdown trading data including long/short selling and margin trading.
        Available for Premium plan users only.
      parameters:
        - name: code
          in: query
          description: Issue code (e.g. 27800 or 2780)
          schema:
            type: string
          example: "86970"
        - name: from
          in: query
          description: Starting point of data period (e.g. 20210901 or 2021-09-01)
          schema:
            type: string
          example: "2023-01-01"
        - name: to
          in: query
          description: End point of data period (e.g. 20210907 or 2021-09-07)
          schema:
            type: string
          example: "2023-12-31"
        - name: date
          in: query
          description: Date of data (e.g. 20210907 or 2021-09-07)
          schema:
            type: string
          example: "2023-03-24"
        - name: pagination_key
          in: query
          description: Pagination key from previous response
          schema:
            type: string
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  breakdown:
                    type: array
                    items:
                      type: object
                      properties:
                        Date:
                          type: string
                          format: date
                          description: Trade date
                        Code:
                          type: string
                          description: Issue code
                        LongSellValue:
                          type: number
                          description: Long selling trading value
                        ShortSellWithoutMarginValue:
                          type: number
                          description: Short selling trading value (excluding new margin sell)
                        MarginSellNewValue:
                          type: number
                          description: New margin selling trading value
                        MarginSellCloseValue:
                          type: number
                          description: Closing margin selling trading value
                        LongBuyValue:
                          type: number
                          description: Long buying trading value
                        MarginBuyNewValue:
                          type: number
                          description: New margin buying trading value
                        MarginBuyCloseValue:
                          type: number
                          description: Closing margin buying trading value
                        LongSellVolume:
                          type: number
                          description: Long selling trading volume
                        ShortSellWithoutMarginVolume:
                          type: number
                          description: Short selling trading volume (excluding new margin sell)
                        MarginSellNewVolume:
                          type: number
                          description: New margin selling trading volume
                        MarginSellCloseVolume:
                          type: number
                          description: Closing margin selling trading volume
                        LongBuyVolume:
                          type: number
                          description: Long buying trading volume
                        MarginBuyNewVolume:
                          type: number
                          description: New margin buying trading volume
                        MarginBuyCloseVolume:
                          type: number
                          description: Closing margin buying trading volume
                  pagination_key:
                    type: string
                    description: Key for retrieving next page of results
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '413':
          description: Payload Too Large
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

tags:
  - name: Authentication
    description: Authentication endpoints for getting tokens
  - name: Listed Companies
    description: Information about listed companies and issues
  - name: Stock Prices
    description: Stock price data including OHLC, volume, and trading values
  - name: Financial Data
    description: Financial statements, earnings, and dividend information
  - name: Market Data
    description: Market trading data by investor type and breakdown data
  - name: Indices
    description: Index data including TOPIX and other market indices
  - name: Derivatives
    description: Futures and options data