#!/usr/bin/env python3
"""Safe BigQuery monitoring - handles missing tables gracefully."""

from google.cloud import bigquery
from datetime import datetime
from tabulate import tabulate

def safe_monitor():
    client = bigquery.Client(project='tokyotickers')
    
    print("\n" + "="*80)
    print("📊 J-QUANTS DATA INGESTION STATUS")
    print("="*80)
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Get all tables that exist
    query = """
    SELECT 
        table_id as table_name,
        row_count,
        ROUND(size_bytes / POW(10, 9), 3) as size_gb,
        TIMESTAMP_DIFF(CURRENT_TIMESTAMP(), creation_time, DAY) as age_days
    FROM `tokyotickers.jquants_data.__TABLES__`
    ORDER BY row_count DESC
    """
    
    print("\n📋 EXISTING TABLES:")
    print("-"*80)
    
    tables = []
    for row in client.query(query):
        tables.append({
            'Table': row.table_name,
            'Records': f"{row.row_count:,}",
            'Size (GB)': f"{row.size_gb:.3f}",
            'Age (days)': row.age_days
        })
    
    if tables:
        print(tabulate(tables, headers='keys', tablefmt='grid'))
    else:
        print("No tables found!")
    
    # Check latest data for existing tables
    print("\n📅 LATEST DATA:")
    print("-"*80)
    
    # Map of table to date column
    table_date_columns = {
        'daily_prices': 'date',
        'companies': 'Date',
        'dividends': 'AnnouncementDate',
        'margin_trading': 'Date',
        'topix_data': 'Date',
        'indices_data': 'Date',
        'trading_by_type': 'Date',
        'short_selling': 'Date',
        'breakdown_trading': 'Date'
    }
    
    for table, date_col in table_date_columns.items():
        try:
            # Check if table exists first
            check_query = f"""
            SELECT table_id 
            FROM `tokyotickers.jquants_data.__TABLES__`
            WHERE table_id = '{table}'
            """
            if list(client.query(check_query)):
                # Table exists, get latest date
                query = f"""
                SELECT 
                    MAX({date_col}) as latest_date,
                    COUNT(*) as record_count,
                    TIMESTAMP_DIFF(CURRENT_TIMESTAMP(), MAX({date_col}), HOUR) as hours_ago
                FROM `tokyotickers.jquants_data.{table}`
                """
                result = list(client.query(query))[0]
                if result.latest_date:
                    print(f"{table:<20} {result.latest_date} ({result.hours_ago}h ago) - {result.record_count:,} records")
                else:
                    print(f"{table:<20} Empty table")
        except Exception as e:
            # Skip if error (table doesn't exist or other issue)
            pass
    
    # Job summary
    print("\n💼 INGESTION JOBS (Last 24h):")
    print("-"*80)
    
    query = """
    SELECT 
        CASE WHEN error_result IS NULL THEN 'SUCCESS' ELSE 'FAILED' END as status,
        COUNT(*) as count,
        STRING_AGG(DISTINCT destination_table.table_id, ', ' LIMIT 5) as tables
    FROM `tokyotickers.region-asia-northeast1.INFORMATION_SCHEMA.JOBS_BY_PROJECT`
    WHERE creation_time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 24 HOUR)
        AND job_type = 'LOAD'
        AND destination_table.dataset_id = 'jquants_data'
    GROUP BY status
    """
    
    for row in client.query(query):
        print(f"{row.status}: {row.count} jobs")
        if row.tables:
            print(f"  Tables: {row.tables}")
    
    # Recent errors
    print("\n❌ RECENT ERRORS (Last 6h):")
    print("-"*80)
    
    query = """
    SELECT 
        TIMESTAMP_TRUNC(creation_time, MINUTE) as time,
        destination_table.table_id as table_name,
        SUBSTR(error_result.message, 0, 100) as error
    FROM `tokyotickers.region-asia-northeast1.INFORMATION_SCHEMA.JOBS_BY_PROJECT`
    WHERE creation_time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 6 HOUR)
        AND job_type = 'LOAD'
        AND destination_table.dataset_id = 'jquants_data'
        AND error_result IS NOT NULL
    ORDER BY creation_time DESC
    LIMIT 10
    """
    
    errors = []
    for row in client.query(query):
        errors.append({
            'Time': row.time.strftime('%H:%M'),
            'Table': row.table_name,
            'Error': row.error[:80] + '...' if len(row.error) > 80 else row.error
        })
    
    if errors:
        print(tabulate(errors, headers='keys', tablefmt='grid'))
    else:
        print("No errors found ✅")
    
    # Coverage summary
    print("\n📊 DATA COVERAGE:")
    print("-"*80)
    
    try:
        # Get company count
        query = "SELECT COUNT(DISTINCT Code) as count FROM `tokyotickers.jquants_data.companies`"
        company_count = list(client.query(query))[0].count
        print(f"Total Companies: {company_count:,}")
        
        # Check coverage for each data type
        coverage_queries = [
            ('Prices', 'SELECT COUNT(DISTINCT code) FROM `tokyotickers.jquants_data.daily_prices`'),
            ('Dividends', 'SELECT COUNT(DISTINCT Code) FROM `tokyotickers.jquants_data.dividends`'),
            ('Margin Trading', 'SELECT COUNT(DISTINCT Code) FROM `tokyotickers.jquants_data.margin_trading`')
        ]
        
        for name, query in coverage_queries:
            try:
                count = list(client.query(query))[0][0]
                pct = (count / company_count * 100) if company_count > 0 else 0
                print(f"  {name}: {count:,} companies ({pct:.1f}%)")
            except:
                print(f"  {name}: No data")
    except:
        print("Unable to calculate coverage")
    
    print("\n" + "="*80)
    print("✅ Monitoring complete")
    print("="*80)

if __name__ == "__main__":
    safe_monitor()