#!/bin/bash

# Monitor J-Quants ingestion progress

PROJECT_ID="tokyotickers"
REGION="asia-northeast1"
JOB_NAME="jquants-backfill"

echo "🔍 Monitoring J-Quants Data Ingestion Progress"
echo "Project: $PROJECT_ID"
echo "=" * 60

# Function to check BigQuery data
check_bigquery_data() {
    echo "📊 BigQuery Data Status:"
    echo "-" * 30
    
    # Check companies
    companies_count=$(bq query --use_legacy_sql=false --format=csv --quiet "SELECT COUNT(*) FROM \`$PROJECT_ID.jquants_data.companies\`" 2>/dev/null | tail -n 1)
    echo "Companies: ${companies_count:-0}"
    
    # Check daily prices
    prices_count=$(bq query --use_legacy_sql=false --format=csv --quiet "SELECT COUNT(*) FROM \`$PROJECT_ID.jquants_data.daily_prices\`" 2>/dev/null | tail -n 1)
    echo "Daily Prices: ${prices_count:-0}"
    
    # Check financial statements
    financials_count=$(bq query --use_legacy_sql=false --format=csv --quiet "SELECT COUNT(*) FROM \`$PROJECT_ID.jquants_data.financial_statements\`" 2>/dev/null | tail -n 1)
    echo "Financial Statements: ${financials_count:-0}"
    
    echo ""
}

# Function to check job status
check_job_status() {
    echo "🚀 Cloud Run Job Status:"
    echo "-" * 30
    
    gcloud run jobs executions list --job $JOB_NAME --region $REGION --limit 1 --format="table(
        metadata.name:label=EXECUTION,
        status.conditions[0].type:label=STATUS,
        status.runningCount:label=RUNNING,
        status.succeededCount:label=SUCCEEDED,
        status.failedCount:label=FAILED,
        metadata.creationTimestamp:label=CREATED
    )"
    echo ""
}

# Function to show recent logs
show_recent_logs() {
    echo "📋 Recent Logs (last 10 lines):"
    echo "-" * 30
    
    # Get the latest execution
    latest_execution=$(gcloud run jobs executions list --job $JOB_NAME --region $REGION --limit 1 --format="value(metadata.name)" 2>/dev/null)
    
    if [ ! -z "$latest_execution" ]; then
        gcloud run jobs executions logs read $latest_execution --region $REGION --limit 10 2>/dev/null || echo "No logs available yet"
    else
        echo "No executions found"
    fi
    echo ""
}

# Main monitoring loop
while true; do
    clear
    echo "🔍 J-Quants Data Ingestion Progress Monitor"
    echo "Time: $(date)"
    echo "=" * 60
    echo ""
    
    check_bigquery_data
    check_job_status
    show_recent_logs
    
    echo "Press Ctrl+C to stop monitoring"
    echo "Refreshing in 30 seconds..."
    
    sleep 30
done
