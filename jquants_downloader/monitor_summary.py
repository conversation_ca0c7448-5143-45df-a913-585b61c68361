#!/usr/bin/env python3
# ABOUTME: Summary monitoring script that shows ingestion progress and alerts on issues
# ABOUTME: Designed to give a quick overview of the ingestion status and health

import json
import os
import sys
from datetime import datetime, timedelta
from tabulate import tabulate

def load_status():
    """Load the latest status from file."""
    status_file = "ingestion_status.json"
    if os.path.exists(status_file):
        with open(status_file, 'r') as f:
            return json.load(f)
    return None

def check_monitor_pid():
    """Check if background monitor is running."""
    pid_file = "monitor.pid"
    if os.path.exists(pid_file):
        with open(pid_file, 'r') as f:
            pid = f.read().strip()
        
        # Check if process is actually running
        try:
            os.kill(int(pid), 0)
            return pid
        except OSError:
            # Process not running, remove stale PID file
            os.remove(pid_file)
    return None

def format_number(n):
    """Format large numbers with K/M suffix."""
    if n >= 1_000_000:
        return f"{n/1_000_000:.1f}M"
    elif n >= 1_000:
        return f"{n/1_000:.1f}K"
    else:
        return str(n)

def main():
    print("=" * 80)
    print("J-QUANTS INGESTION MONITOR SUMMARY")
    print("=" * 80)
    print(f"Current Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Check monitor status
    monitor_pid = check_monitor_pid()
    if monitor_pid:
        print(f"✅ Background monitor is running (PID: {monitor_pid})")
    else:
        print("❌ Background monitor is NOT running!")
        print("   Start it with: python background_monitor.py &")
        print()
    
    # Load status
    status = load_status()
    if not status:
        print("❌ No status file found. Monitor may not have run yet.")
        sys.exit(1)
    
    # Check timestamp
    status_time = datetime.fromisoformat(str(status['timestamp']))
    age_minutes = (datetime.now() - status_time).total_seconds() / 60
    
    print(f"📊 Last Update: {status_time.strftime('%Y-%m-%d %H:%M:%S')} ({int(age_minutes)} minutes ago)")
    
    if age_minutes > 5:
        print("⚠️  WARNING: Status is stale! Monitor may have stopped.")
    
    print()
    
    # Show progress
    new_records = status.get('new_records_this_check', 0)
    if new_records > 0:
        print(f"✅ ACTIVE INGESTION - {format_number(new_records)} new records in last check")
        
        changes = status.get('recent_changes', {})
        if changes:
            print("\n📈 Recent Changes:")
            for table, count in sorted(changes.items(), key=lambda x: x[1], reverse=True):
                print(f"   {table:<25} +{count:,}")
    else:
        print("⏸️  NO ACTIVE INGESTION - No new records in last check")
    
    print()
    
    # Show data summary
    total = status.get('total_records', 0)
    print(f"📊 Total Records in Database: {format_number(total)} ({total:,})")
    
    # Show table breakdown
    counts = status.get('data_counts', {})
    if counts:
        print("\n📋 Data by Table:")
        
        table_data = []
        for table, count in sorted(counts.items()):
            if count > 0:
                table_data.append({
                    'Table': table,
                    'Records': format_number(count),
                    'Percentage': f"{(count/total*100):.1f}%" if total > 0 else "0%"
                })
        
        if table_data:
            print(tabulate(table_data, headers='keys', tablefmt='simple'))
    
    # Check for errors
    errors = status.get('recent_errors', [])
    if errors:
        print("\n⚠️  RECENT ERRORS DETECTED:")
        for error in errors[:3]:
            print(f"   {error.get('time', 'Unknown time')}: {error.get('message', '')[:100]}")
    
    print("\n" + "=" * 80)
    
    # Show commands
    print("📌 Useful Commands:")
    print("   - Check detailed logs: tail -f ingestion_monitor.log")
    print("   - Check full status: python check_status.py")
    print("   - Trigger new ingestion: python trigger_ingestion.py")
    print("   - Stop monitor: kill $(cat monitor.pid)")

if __name__ == "__main__":
    main()