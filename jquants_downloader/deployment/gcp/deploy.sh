#!/bin/bash

# Deploy J-Quants Data Ingestion Service to GCP
# This script sets up the complete production infrastructure

set -e

# Configuration
PROJECT_ID="tokyotickers"
REGION="asia-northeast1"
SERVICE_NAME="jquants-ingestion"
SERVICE_ACCOUNT="<EMAIL>"
BUCKET_NAME="tokyotickers-jquants-state"

echo "🚀 Deploying J-Quants Data Ingestion Service to GCP"
echo "Project: $PROJECT_ID"
echo "Region: $REGION"
echo "Service: $SERVICE_NAME"
echo ""

# Set the project
gcloud config set project $PROJECT_ID

# Enable required APIs
echo "📋 Enabling required APIs..."
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable cloudscheduler.googleapis.com
gcloud services enable bigquery.googleapis.com
gcloud services enable storage.googleapis.com

# Build and push the container image
echo "🔨 Building container image..."
gcloud builds submit --tag gcr.io/$PROJECT_ID/$SERVICE_NAME .

# Deploy to Cloud Run for daily updates
echo "🚀 Deploying to Cloud Run..."
gcloud run deploy $SERVICE_NAME \
    --image gcr.io/$PROJECT_ID/$SERVICE_NAME \
    --platform managed \
    --region $REGION \
    --service-account $SERVICE_ACCOUNT \
    --memory 2Gi \
    --cpu 1 \
    --timeout 3600 \
    --max-instances 1 \
    --no-allow-unauthenticated \
    --set-env-vars "GOOGLE_CLOUD_PROJECT=$PROJECT_ID" \
    --set-env-vars "BIGQUERY_DATASET=jquants_data" \
    --set-env-vars "JQUANTS_EMAIL=<EMAIL>" \
    --set-env-vars "JQUANTS_PASSWORD=Borboletas747"

# Get the Cloud Run service URL
SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --region $REGION --format 'value(status.url)')
echo "Service URL: $SERVICE_URL"

# Create Cloud Scheduler job for daily updates
echo "📅 Setting up Cloud Scheduler for daily updates..."
gcloud scheduler jobs create http jquants-daily-update \
    --location $REGION \
    --schedule "0 6 * * *" \
    --uri "$SERVICE_URL/trigger" \
    --http-method POST \
    --headers "Content-Type=application/json" \
    --message-body '{"mode": "daily-update"}' \
    --oidc-service-account-email $SERVICE_ACCOUNT \
    --time-zone "Asia/Tokyo" \
    --description "Daily J-Quants data update" \
    --max-retry-attempts 3 \
    --max-retry-duration 3600s \
    2>/dev/null || echo "Scheduler job already exists, trying to update..."

# Delete and recreate if update fails
gcloud scheduler jobs delete jquants-daily-update --location $REGION --quiet 2>/dev/null || true

gcloud scheduler jobs create http jquants-daily-update \
    --location $REGION \
    --schedule "0 6 * * *" \
    --uri "$SERVICE_URL/trigger" \
    --http-method POST \
    --headers "Content-Type=application/json" \
    --message-body '{"mode": "daily-update"}' \
    --oidc-service-account-email $SERVICE_ACCOUNT \
    --time-zone "Asia/Tokyo" \
    --description "Daily J-Quants data update" \
    --max-retry-attempts 3 \
    --max-retry-duration 3600s \
    2>/dev/null || echo "Scheduler job creation completed"

echo ""
echo "✅ Deployment complete!"
echo ""
echo "📋 Next steps:"
echo "1. Run full historical backfill:"
echo "   gcloud run jobs execute jquants-backfill --region $REGION"
echo ""
echo "2. Monitor daily updates:"
echo "   gcloud scheduler jobs list --location $REGION"
echo ""
echo "3. Check logs:"
echo "   gcloud logs read --project $PROJECT_ID --filter 'resource.type=cloud_run_revision'"
echo ""
echo "4. Manual execution:"
echo "   curl -X POST $SERVICE_URL -H 'Authorization: Bearer \$(gcloud auth print-identity-token)' -H 'Content-Type: application/json' -d '{\"mode\": \"daily-update\"}'"
