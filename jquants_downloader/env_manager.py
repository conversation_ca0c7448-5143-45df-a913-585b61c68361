#!/usr/bin/env python3
"""Environment manager for handling multiple GCP projects."""

import os
import sys
import shutil
from pathlib import Path
from typing import List, Optional


class EnvironmentManager:
    """Manages multiple environment configurations for different GCP projects."""

    def __init__(self, base_dir: str = "."):
        """Initialize environment manager.

        Args:
            base_dir: Base directory containing environment files
        """
        self.base_dir = Path(base_dir)
        self.env_file = self.base_dir / ".env"
        self.env_example = self.base_dir / ".env.example"

    def list_environments(self) -> List[str]:
        """List all available environment configurations.

        Returns:
            List of environment names
        """
        env_files = list(self.base_dir.glob(".env.*"))
        environments = []

        for env_file in env_files:
            if env_file.name != ".env.example":
                env_name = env_file.name.replace(".env.", "")
                environments.append(env_name)

        return sorted(environments)

    def get_current_environment(self) -> Optional[str]:
        """Get the currently active environment.

        Returns:
            Current environment name or None if no environment is active
        """
        if not self.env_file.exists():
            return None

        # Try to determine which environment is active by comparing content
        current_content = self.env_file.read_text()

        for env_name in self.list_environments():
            env_file_path = self.base_dir / f".env.{env_name}"
            if env_file_path.exists():
                env_content = env_file_path.read_text()
                # Simple comparison - check if project names match
                if self._extract_project_id(current_content) == self._extract_project_id(env_content):
                    return env_name

        return "custom"

    def _extract_project_id(self, content: str) -> Optional[str]:
        """Extract project ID from environment file content.

        Args:
            content: Environment file content

        Returns:
            Project ID or None if not found
        """
        for line in content.split('\n'):
            if line.startswith('GOOGLE_CLOUD_PROJECT='):
                return line.split('=', 1)[1].strip()
        return None

    def switch_environment(self, env_name: str) -> bool:
        """Switch to a specific environment.

        Args:
            env_name: Name of the environment to switch to

        Returns:
            True if successful, False otherwise
        """
        env_file_path = self.base_dir / f".env.{env_name}"

        if not env_file_path.exists():
            print(f"Environment '{env_name}' not found.")
            print(f"Available environments: {', '.join(self.list_environments())}")
            return False

        try:
            # Backup current .env if it exists
            if self.env_file.exists():
                backup_path = self.base_dir / ".env.backup"
                shutil.copy2(self.env_file, backup_path)
                print(f"Backed up current .env to {backup_path}")

            # Copy the selected environment to .env
            shutil.copy2(env_file_path, self.env_file)
            print(f"Switched to environment: {env_name}")

            # Show current project info
            self.show_current_info()
            return True

        except Exception as e:
            print(f"Error switching environment: {e}")
            return False

    def create_environment(self, env_name: str, project_id: str, gcp_account: str, region: str = "asia-northeast1") -> bool:
        """Create a new environment configuration.

        Args:
            env_name: Name for the new environment
            project_id: GCP project ID
            gcp_account: GCP account email
            region: GCP region

        Returns:
            True if successful, False otherwise
        """
        env_file_path = self.base_dir / f".env.{env_name}"

        if env_file_path.exists():
            print(f"Environment '{env_name}' already exists.")
            return False

        try:
            # Use example as template
            if self.env_example.exists():
                content = self.env_example.read_text()
            else:
                content = self._get_default_env_template()

            # Update with specific values
            content = content.replace("tokyo_tickers", project_id)
            content = content.replace("asia-northeast1", region)
            content = content.replace("service-account-key.json", f"{project_id}_service_account.json")
            content = content.replace("<EMAIL>", gcp_account)

            # Add GCP account info as comment
            content = f"# GCP Account: {gcp_account}\n" + content

            # Write new environment file
            env_file_path.write_text(content)
            print(f"Created environment '{env_name}' for project '{project_id}' (account: {gcp_account}) in region '{region}'")
            return True

        except Exception as e:
            print(f"Error creating environment: {e}")
            return False

    def _get_default_env_template(self) -> str:
        """Get default environment template.

        Returns:
            Default environment file content
        """
        return """# J-Quants API Configuration
JQUANTS_EMAIL=<EMAIL>
JQUANTS_PASSWORD=your-password
JQUANTS_REFRESH_TOKEN=your-refresh-token-here

# Google Cloud Configuration
GOOGLE_CLOUD_PROJECT=tokyo_tickers
GOOGLE_APPLICATION_CREDENTIALS=service-account-key.json

# BigQuery Configuration
BIGQUERY_DATASET=jquants_data
BIGQUERY_LOCATION=asia-northeast1

# Optional: Logging Configuration
LOG_LEVEL=INFO
"""

    def show_current_info(self) -> None:
        """Show information about the current environment."""
        current_env = self.get_current_environment()

        if not self.env_file.exists():
            print("No active environment (.env file not found)")
            return

        content = self.env_file.read_text()
        project_id = self._extract_project_id(content)

        print(f"\nCurrent Environment: {current_env or 'Unknown'}")
        print(f"Project ID: {project_id or 'Not set'}")

        # Extract other key info
        gcp_account = None
        for line in content.split('\n'):
            if line.startswith('# GCP Account:'):
                gcp_account = line.split(':', 1)[1].strip()
            elif line.startswith('BIGQUERY_LOCATION='):
                location = line.split('=', 1)[1].strip()
                print(f"BigQuery Location: {location}")
            elif line.startswith('GOOGLE_APPLICATION_CREDENTIALS='):
                creds = line.split('=', 1)[1].strip()
                print(f"Credentials File: {creds}")

        if gcp_account:
            print(f"GCP Account: {gcp_account}")

    def list_info(self) -> None:
        """List all environments with their information."""
        environments = self.list_environments()
        current_env = self.get_current_environment()

        print("Available Environments:")
        print("=" * 50)

        if not environments:
            print("No environments found.")
            print("Create one with: python env_manager.py create <env_name> <project_id>")
            return

        for env_name in environments:
            env_file_path = self.base_dir / f".env.{env_name}"
            content = env_file_path.read_text()
            project_id = self._extract_project_id(content)

            # Extract GCP account
            gcp_account = "Unknown"
            for line in content.split('\n'):
                if line.startswith('# GCP Account:'):
                    gcp_account = line.split(':', 1)[1].strip()
                    break

            status = " (ACTIVE)" if env_name == current_env else ""
            print(f"{env_name}: {project_id} ({gcp_account}){status}")


def main():
    """Main function for environment manager CLI."""
    import argparse

    parser = argparse.ArgumentParser(description="Manage multiple GCP project environments")
    subparsers = parser.add_subparsers(dest="command", help="Available commands")

    # List command
    list_parser = subparsers.add_parser("list", help="List all environments")

    # Switch command
    switch_parser = subparsers.add_parser("switch", help="Switch to an environment")
    switch_parser.add_argument("env_name", help="Environment name to switch to")

    # Create command
    create_parser = subparsers.add_parser("create", help="Create new environment")
    create_parser.add_argument("env_name", help="Environment name")
    create_parser.add_argument("project_id", help="GCP project ID")
    create_parser.add_argument("gcp_account", help="GCP account email")
    create_parser.add_argument("--region", default="asia-northeast1", help="GCP region")

    # Current command
    current_parser = subparsers.add_parser("current", help="Show current environment info")

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return

    manager = EnvironmentManager()

    if args.command == "list":
        manager.list_info()
    elif args.command == "switch":
        success = manager.switch_environment(args.env_name)
        sys.exit(0 if success else 1)
    elif args.command == "create":
        success = manager.create_environment(args.env_name, args.project_id, args.gcp_account, args.region)
        sys.exit(0 if success else 1)
    elif args.command == "current":
        manager.show_current_info()


if __name__ == "__main__":
    main()
