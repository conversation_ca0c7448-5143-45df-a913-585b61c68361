# GCP Account: <EMAIL>
# J-Quants API Configuration
JQUANTS_EMAIL="<EMAIL>"
JQUANTS_PASSWORD="Borboletas747"
JQUANTS_REFRESH_TOKEN="eyJjdHkiOiJKV1QiLCJlbmMiOiJBMjU2R0NNIiwiYWxnIjoiUlNBLU9BRVAifQ.pEuDaGlQbr9--mFTNOCU6VbShmmpho3jIHSKCgEqpKQeGKLntDP0oR8szEa0ZB6IMQw8BMV8jfDrmOIOur2Tp8nA5X-zJIAqHHyyLGxVUvehxFpTexSQ7g6MdUuMay12r7G6kTkEDJWBptLn8PToE09c-rdbMuFfyGeOX4xkbh5F51e20N73ISq30io0o7R4jVkStB1yf387F-tQq5C6rJa60OSS_WcxJdIS-H8wns5Fg0td-WSNVVZeQGHtOMHwGIG-_Dr6hlWBQV2bJfI5sX2yDtMJvStmEPuCqIORt1A-x23zIu8e1L6j7RxSlhgtvuyP4EHjgn34vkOYpyaiUQ.W-jaPE24zOINdXNW.RSghqF2TFHnpSiv6bKg771WuuOkH_3UvKHexY5WzIFsalFf7m6HDePAWbWurM73o_qx1sn9qkMasjnTaX2Jvx39gu2PWwR10NyttV8kyPA0AeMzx1R1rtcyJokSyL7RjyLE10r7tB944Tu6cuOIVjVZgxHSsk6iZ4elrzbv7ILPE53PV1YMM0WxcnX05qg3GMMqa9A3yIBTNX798t1AJox3DqF1Ldhm4eK5uMoPV-C_SgaGpT_KTXziRHnfG4fNLne0Ws6eN0cy8FwumN28VNPvy9kquWVKLdYEPCe_3vSMkPiV1EdDod_2ZeKB8yO8SJtgGP-trBa-S2WZEEjFc3Q1NVoaLmvDOb7JrCWM_BV0RNJay-WijOclfUyzC-GVDZ4AwDg6seLz5EcN403ts7asJZIUbv7d1Jly_e9Ui5jqNpqIgWUNxqpkq-7usb0zy2XhMFPguCiTuCBVqtJnHfh_GX6qaFbRkJWlv6lxGVCOwSA-AmJ8in7cR2S5JbaBVuFcFEYGQlYqicZ-RpXfl2-lfwuE4l4dyqQPN2sHjipqcwvIsJXofe6OIWXB1HJuAOV2cOZ8C67hDXg324-gcLiRZGsyadOXK_jSticU8HbpAZE3LS9FDqJpeyX4HlMvgyfvNoPUTlWhl6h4EyoWv0AYvtzWzn07jIXauhW3QDL3Y63AojQTZntApsWNbbFEmazV-vlhsMX-WrwQqVKMaxptiA9wEiHJtuTg4PG5yfwrf4WJb9GDZ4uuPaB-O55Apw5AkbpSM0T6H3Dazeawc2Bzg5lzcpNwj7CFYEKCWCjPJ3bPKaBg_NlcOTA-9PFG99z6z-fTTenbeKAphZBJ8_qAl-4P1cesa-vSn6Yb4Uc-pEr1Tuj8EAjhURW6CNQ2Lw6LisoG4v9UByNG0b5acUA4B8z63FR-gHNwpUxiOg_er4IFsNg71o9QWLkT0y4NT4cmfA9IzA8g-WbgahkZHOIL8Up6fGLLQJEKmWk4sVQnMd7r2cl5-kyjaTzotfcBXlWVSUrYHINsqtXqfMWuXJba3rz1XqCNzOhjWAuwRUMNws7oEPzK2hgyC129qT2LBV06ZfJQW_NNSx46Lctuk7PhQZ5V6CkZkAy-z6tJ3WW9LEAee0G28ckYloOBj6sYc4rTTo0DJqAxRyHQELeLTm4KgHLZl6fi7uVDC9Ua-zJ01oFyY7ykB_iJKM2QKoeug6huzb0HTMFWWno3_-TVqL5tP5dILiTqVU7LsB5yomOtOCEXgoQnU65UwxKPIE7tqOI4tenB5tL1TdXm5-vYvY-xzGlGcnYOh4NYAu5zWxVXOqfxJYtXep6dxujIM-S2FnFYu_H7QYoiDtg.BN2QRC7gIvm1MGqttFSTVw"

# Google Cloud Configuration - Tokyo Tickers Project
GOOGLE_CLOUD_PROJECT=tokyo_tickers
GOOGLE_APPLICATION_CREDENTIALS=tokyo_tickers_service_account.json

# BigQuery Configuration - Tokyo Region
BIGQUERY_DATASET=jquants_data
BIGQUERY_LOCATION=asia-northeast1

# Optional: Logging Configuration
LOG_LEVEL=INFO
