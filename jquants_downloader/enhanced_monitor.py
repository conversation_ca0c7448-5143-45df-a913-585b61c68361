#!/usr/bin/env python3
# ABOUTME: Enhanced monitoring script that checks ingestion status with authentication and automatic recovery
# ABOUTME: Includes periodic health checks, error detection, and automatic retriggering when needed

import time
import requests
import subprocess
import sys
from datetime import datetime, timedelta
from google.cloud import bigquery
import logging
import json

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class EnhancedIngestionMonitor:
    def __init__(self):
        self.client = bigquery.Client(project='tokyotickers')
        self.base_url = "https://jquants-ingestion-621634133093.asia-northeast1.run.app"
        self.last_data_counts = {}
        self.last_check_time = None
        self.consecutive_no_progress = 0
        self.max_no_progress = 6  # 12 minutes of no progress before retrigger
        self.last_error_time = None
        self.auth_token = None
        self.token_expiry = None
        
    def get_auth_token(self, force_refresh=False):
        """Get or refresh authentication token for Cloud Run service."""
        # Check if we have a valid token
        if not force_refresh and self.auth_token and self.token_expiry:
            if datetime.now() < self.token_expiry:
                return self.auth_token
        
        try:
            logger.info("🔑 Getting new authentication token...")
            result = subprocess.run(
                ['gcloud', 'auth', 'print-identity-token'],
                capture_output=True,
                text=True,
                timeout=10
            )
            if result.returncode == 0:
                self.auth_token = result.stdout.strip()
                # Token is valid for about 1 hour, refresh after 50 minutes
                self.token_expiry = datetime.now() + timedelta(minutes=50)
                logger.info("✅ Authentication token obtained")
                return self.auth_token
            else:
                logger.error(f"Failed to get auth token: {result.stderr}")
                return None
        except Exception as e:
            logger.error(f"Error getting auth token: {e}")
            return None
    
    def make_authenticated_request(self, endpoint, method='GET', json_data=None, retry_auth=True):
        """Make an authenticated request to the Cloud Run service."""
        token = self.get_auth_token()
        if not token:
            logger.error("❌ Could not get authentication token")
            return None
        
        url = f"{self.base_url}{endpoint}"
        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
        
        try:
            if method == 'GET':
                response = requests.get(url, headers=headers, timeout=30)
            else:
                response = requests.post(url, headers=headers, json=json_data, timeout=30)
            
            # If we get 401/403, try refreshing token once
            if response.status_code in [401, 403] and retry_auth:
                logger.warning("🔄 Authentication failed, refreshing token...")
                token = self.get_auth_token(force_refresh=True)
                if token:
                    headers['Authorization'] = f'Bearer {token}'
                    return self.make_authenticated_request(endpoint, method, json_data, retry_auth=False)
            
            return response
        except Exception as e:
            logger.error(f"Request error: {e}")
            return None
    
    def check_service_health(self):
        """Check if the Cloud Run service is healthy."""
        response = self.make_authenticated_request('/')
        if response and response.status_code == 200:
            return True, "Service is healthy"
        elif response:
            return False, f"Service returned {response.status_code}: {response.text}"
        else:
            return False, "Could not reach service"
    
    def check_ingestion_status(self):
        """Check the current ingestion status."""
        response = self.make_authenticated_request('/status')
        if response and response.status_code == 200:
            try:
                return response.json()
            except:
                return {"status": "unknown", "message": response.text}
        else:
            return {"status": "error", "message": f"Could not get status: {response.status_code if response else 'No response'}"}
    
    def trigger_ingestion(self, mode="daily-update"):
        """Trigger the ingestion service."""
        logger.info(f"🚀 Triggering ingestion with mode: {mode}")
        response = self.make_authenticated_request('/trigger', method='POST', json_data={"mode": mode})
        
        if response and response.status_code == 200:
            logger.info("✅ Successfully triggered ingestion service")
            return True
        else:
            error_msg = f"{response.status_code}: {response.text}" if response else "No response"
            logger.error(f"❌ Failed to trigger service: {error_msg}")
            return False
    
    def get_current_data_counts(self):
        """Get current row counts for all tables."""
        counts = {}
        tables = ['companies', 'daily_prices', 'dividends', 'margin_trading', 'topix_data', 
                 'indices_data', 'trading_by_type', 'short_selling', 'breakdown_trading',
                 'financial_statements', 'options_data', 'futures_data']
        
        for table in tables:
            try:
                query = f"SELECT COUNT(*) as count FROM `tokyotickers.jquants_data.{table}`"
                result = list(self.client.query(query))[0]
                counts[table] = result.count
            except Exception as e:
                if "not found" in str(e).lower():
                    counts[table] = 0
                else:
                    logger.warning(f"Error checking {table}: {e}")
                    counts[table] = -1
        
        return counts
    
    def check_recent_logs(self, minutes=5):
        """Check for recent errors in Cloud Run logs."""
        try:
            result = subprocess.run([
                'gcloud', 'run', 'services', 'logs', 'read', 'jquants-ingestion',
                '--region=asia-northeast1', '--limit=20'
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                logs = result.stdout
                errors = []
                warnings = []
                
                for line in logs.split('\n'):
                    if "ERROR" in line or "FAILED" in line:
                        errors.append(line.strip())
                    elif "WARNING" in line or "429" in line:
                        warnings.append(line.strip())
                
                return errors[:3], warnings[:3]  # Return last 3 of each
            return [], []
        except Exception as e:
            logger.warning(f"Could not check logs: {e}")
            return [], []
    
    def print_detailed_status(self, counts, progress_detected, service_status, ingestion_status, errors, warnings):
        """Print detailed status in a nice format."""
        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"\n{'='*80}")
        print(f"📊 INGESTION MONITOR - {now}")
        print(f"{'='*80}")
        
        # Service health
        health_ok, health_msg = service_status
        health_icon = "✅" if health_ok else "❌"
        print(f"\n{health_icon} Service Health: {health_msg}")
        
        # Ingestion status
        if ingestion_status:
            status_icon = "🔄" if ingestion_status.get("status") == "running" else "⏸️"
            print(f"{status_icon} Ingestion Status: {ingestion_status.get('status', 'unknown')}")
            if ingestion_status.get("message"):
                print(f"   Message: {ingestion_status['message']}")
        
        # Data counts
        total_records = sum(count for count in counts.values() if count > 0)
        print(f"\n📋 Data Status (Total: {total_records:,} records):")
        print(f"{'Table':<25} {'Count':>12} {'Change':>12}")
        print("-" * 50)
        
        for table, count in sorted(counts.items()):
            if count > 0:
                # Check for progress
                if table in self.last_data_counts:
                    diff = count - self.last_data_counts[table]
                    if diff > 0:
                        print(f"{table:<25} {count:>12,} {f'+{diff:,}':>12} 📈")
                    else:
                        print(f"{table:<25} {count:>12,} {'':>12}")
                else:
                    print(f"{table:<25} {count:>12,} {'NEW':>12} 🆕")
            elif count == 0:
                print(f"{table:<25} {'No data':>12} {'':>12}")
        
        # Progress tracking
        print(f"\n⏱️  Progress Tracking:")
        if progress_detected:
            print(f"   ✅ Active ingestion detected")
            self.consecutive_no_progress = 0
        else:
            self.consecutive_no_progress += 1
            print(f"   ⏸️  No progress for {self.consecutive_no_progress * 2} minutes")
            if self.consecutive_no_progress >= self.max_no_progress:
                print(f"   🚨 Will retrigger ingestion soon!")
        
        # Recent errors and warnings
        if errors:
            print(f"\n❌ Recent Errors:")
            for error in errors[:2]:
                print(f"   {error[:100]}...")
        
        if warnings:
            print(f"\n⚠️  Recent Warnings:")
            for warning in warnings[:2]:
                print(f"   {warning[:100]}...")
        
        print(f"\n{'='*80}")
    
    def run_monitoring_loop(self, check_interval=120):
        """Main monitoring loop with enhanced error handling."""
        logger.info("🚀 Starting enhanced monitoring...")
        logger.info(f"📊 Checking every {check_interval} seconds")
        logger.info("🔄 Auto-retrigger enabled after 12 minutes of no progress")
        logger.info("❌ Press Ctrl+C to stop")
        
        # Initial trigger if requested
        if len(sys.argv) > 1 and sys.argv[1] == "--trigger":
            logger.info("🚀 Initial trigger requested...")
            self.trigger_ingestion()
        
        try:
            while True:
                try:
                    # Check service health
                    service_status = self.check_service_health()
                    
                    # Get ingestion status
                    ingestion_status = self.check_ingestion_status()
                    
                    # Get current data counts
                    current_counts = self.get_current_data_counts()
                    
                    # Check for progress
                    progress_detected = False
                    if self.last_data_counts:
                        for table, count in current_counts.items():
                            if count > self.last_data_counts.get(table, 0):
                                progress_detected = True
                                break
                    
                    # Check for recent errors
                    errors, warnings = self.check_recent_logs()
                    
                    # Print detailed status
                    self.print_detailed_status(
                        current_counts, progress_detected, 
                        service_status, ingestion_status,
                        errors, warnings
                    )
                    
                    # Auto-retrigger logic
                    if (not progress_detected and 
                        self.consecutive_no_progress >= self.max_no_progress and
                        self.last_data_counts):  # Don't retrigger on first run
                        
                        logger.warning("🚨 No progress detected for 12+ minutes")
                        
                        # Check if service is healthy before retriggering
                        if service_status[0]:
                            logger.info("🔄 Service is healthy, retriggering ingestion...")
                            if self.trigger_ingestion():
                                self.consecutive_no_progress = 0
                                logger.info("✅ Service retriggered successfully")
                            else:
                                logger.error("❌ Failed to retrigger")
                        else:
                            logger.error("❌ Service is unhealthy, skipping retrigger")
                    
                    # Update state
                    self.last_data_counts = current_counts.copy()
                    self.last_check_time = datetime.now()
                    
                    # Wait for next check
                    logger.info(f"⏳ Next check in {check_interval} seconds...")
                    time.sleep(check_interval)
                    
                except Exception as e:
                    logger.error(f"❌ Error in monitoring loop: {e}")
                    logger.info("🔄 Continuing monitoring...")
                    time.sleep(30)  # Short wait before retry
                    
        except KeyboardInterrupt:
            logger.info("👋 Monitoring stopped by user")
        except Exception as e:
            logger.error(f"❌ Fatal monitoring error: {e}")
            raise

def main():
    print("🚀 Enhanced JQuants Ingestion Monitor")
    print("Options:")
    print("  python enhanced_monitor.py           - Start monitoring")
    print("  python enhanced_monitor.py --trigger - Trigger ingestion and monitor")
    print("")
    
    monitor = EnhancedIngestionMonitor()
    monitor.run_monitoring_loop()

if __name__ == "__main__":
    main()