#!/usr/bin/env python3
# ABOUTME: <PERSON>ript to fix all insert_dataframe return value handling in production_ingestion_service.py
# ABOUTME: Changes count = insert_dataframe() to properly handle the job_id return value

import re

# Read the file
with open('production_ingestion_service.py', 'r') as f:
    content = f.read()

# Pattern to find count = self.bigquery_client.insert_dataframe calls
pattern = r'(\s+)count = self\.bigquery_client\.insert_dataframe\(([\s\S]*?)\)'

def replacement(match):
    indent = match.group(1)
    params = match.group(2)
    
    # Extract the dataframe parameter name
    # Look for patterns like:
    # - dataframe=margin_data
    # - margin_data,
    # - table_name='margin_trading', dataframe=margin_data
    
    # Try to find dataframe= pattern
    dataframe_match = re.search(r'dataframe=([a-zA-Z_]+)', params)
    if dataframe_match:
        df_name = dataframe_match.group(1)
    else:
        # Try to find first parameter (old style)
        lines = params.strip().split('\n')
        first_param = lines[0].strip().rstrip(',')
        # If it's not a string (table name), it's probably the dataframe
        if not first_param.startswith(("'", '"')):
            df_name = first_param
        else:
            # Can't determine dataframe name
            return match.group(0)  # Return unchanged
    
    # Build the replacement
    replacement_text = f"""{indent}job_id = self.bigquery_client.insert_dataframe({params})
{indent}
{indent}# Count actual records inserted
{indent}count = len({df_name})"""
    
    return replacement_text

# Apply the replacement
new_content = re.sub(pattern, replacement, content)

# Write back
with open('production_ingestion_service.py', 'w') as f:
    f.write(new_content)

print("✅ Fixed all insert_dataframe return value handling")