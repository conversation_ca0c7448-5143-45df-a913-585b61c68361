#!/bin/bash
# ABOUTME: Shell script to trigger ingestion and start continuous monitoring
# ABOUTME: Ensures proper authentication and handles errors gracefully

set -e

echo "🚀 JQuants Ingestion Monitor with Trigger"
echo "========================================"
echo ""

# Check if gcloud is authenticated
echo "🔑 Checking gcloud authentication..."
if ! gcloud auth list --format="value(account)" | grep -q .; then
    echo "❌ Not authenticated with gcloud. Please run: gcloud auth login"
    exit 1
fi

echo "✅ Authenticated as: $(gcloud auth list --filter=status:ACTIVE --format="value(account)")"
echo ""

# Get authentication token
echo "🔐 Getting Cloud Run authentication token..."
TOKEN=$(gcloud auth print-identity-token)
if [ -z "$TOKEN" ]; then
    echo "❌ Failed to get authentication token"
    exit 1
fi

echo "✅ Got authentication token"
echo ""

# Trigger ingestion
echo "🚀 Triggering ingestion service..."
SERVICE_URL="https://jquants-ingestion-************.asia-northeast1.run.app"

RESPONSE=$(curl -s -w "\n%{http_code}" -X POST "$SERVICE_URL/trigger" \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json" \
    -d '{"mode": "daily-update"}')

HTTP_CODE=$(echo "$RESPONSE" | tail -n1)
BODY=$(echo "$RESPONSE" | head -n-1)

if [ "$HTTP_CODE" = "200" ]; then
    echo "✅ Successfully triggered ingestion!"
    echo "   Response: $BODY"
else
    echo "❌ Failed to trigger ingestion"
    echo "   HTTP Code: $HTTP_CODE"
    echo "   Response: $BODY"
    echo ""
    echo "Do you want to continue with monitoring anyway? (y/n)"
    read -r answer
    if [ "$answer" != "y" ]; then
        exit 1
    fi
fi

echo ""
echo "📊 Starting continuous monitoring..."
echo "   - Checks every 2 minutes"
echo "   - Auto-retriggers if no progress for 12 minutes"
echo "   - Press Ctrl+C to stop"
echo ""

# Start enhanced monitoring
cd /home/<USER>/dev/pair/jquants_downloader
python enhanced_monitor.py