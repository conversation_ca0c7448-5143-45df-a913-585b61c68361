#!/usr/bin/env python3
"""Diagnose J-Quants API rate limiting issues."""

import logging
import time
import sys
from datetime import datetime
import requests
from dotenv import load_dotenv

# Add src to path
sys.path.insert(0, 'src')

from jquants_downloader.config import Config

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_direct_api_call():
    """Test direct API call to understand rate limiting."""
    logger.info("🔍 Testing direct API calls...")
    
    # Test 1: Basic auth endpoint
    logger.info("1️⃣  Testing auth endpoint...")
    try:
        auth_url = "https://api.jquants.com/v1/token/auth_user"
        auth_data = {
            "mailaddress": Config.JQUANTS_EMAIL,
            "password": Config.JQUANTS_PASSWORD
        }
        
        response = requests.post(auth_url, json=auth_data, timeout=30)
        logger.info(f"   Status: {response.status_code}")
        logger.info(f"   Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            logger.info("   ✅ Auth successful")
        elif response.status_code == 429:
            logger.error("   ❌ Rate limited on auth endpoint!")
            logger.info(f"   Response: {response.text}")
        else:
            logger.warning(f"   ⚠️  Unexpected status: {response.text}")
            
    except Exception as e:
        logger.error(f"   💥 Auth test failed: {e}")
    
    # Wait before next test
    logger.info("⏱️  Waiting 60 seconds before next test...")
    time.sleep(60)
    
    # Test 2: Simple data endpoint (if auth worked)
    logger.info("2️⃣  Testing simple data endpoint...")
    try:
        # Try to get company info (minimal request)
        data_url = "https://api.jquants.com/v1/listed/info"
        params = {"code": "86970"}  # Sony - single company
        
        response = requests.get(data_url, params=params, timeout=30)
        logger.info(f"   Status: {response.status_code}")
        logger.info(f"   Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            logger.info("   ✅ Data request successful")
            data = response.json()
            logger.info(f"   Records: {len(data.get('info', []))}")
        elif response.status_code == 401:
            logger.warning("   🔐 Unauthorized - need proper authentication")
        elif response.status_code == 429:
            logger.error("   ❌ Rate limited on data endpoint!")
            logger.info(f"   Response: {response.text}")
        else:
            logger.warning(f"   ⚠️  Unexpected status: {response.text}")
            
    except Exception as e:
        logger.error(f"   💥 Data test failed: {e}")


def test_jquants_library():
    """Test the official jquantsapi library."""
    logger.info("📚 Testing official jquantsapi library...")
    
    try:
        from jquantsapi import Client
        
        client = Client(
            mail_address=Config.JQUANTS_EMAIL,
            password=Config.JQUANTS_PASSWORD
        )
        
        logger.info("   ✅ Client initialized")
        
        # Try minimal request
        logger.info("   🔍 Testing minimal request (Sony company info)...")
        
        start_time = time.time()
        result = client.get_listed_info(code="86970")
        end_time = time.time()
        
        logger.info(f"   ⏱️  Request took {end_time - start_time:.2f} seconds")
        logger.info(f"   📊 Result type: {type(result)}")
        
        if hasattr(result, 'empty'):
            logger.info(f"   📈 Records: {len(result) if not result.empty else 0}")
            if not result.empty:
                logger.info("   ✅ Library test successful")
            else:
                logger.warning("   ⚠️  Empty result returned")
        else:
            logger.info(f"   📋 Result: {result}")
            
    except Exception as e:
        error_msg = str(e)
        if "429" in error_msg or "too many" in error_msg.lower():
            logger.error("   ❌ Rate limited via library!")
        else:
            logger.error(f"   💥 Library test failed: {e}")


def analyze_rate_limit_headers(response):
    """Analyze rate limit headers if present."""
    logger.info("🔍 Analyzing rate limit headers...")
    
    rate_headers = [
        'X-RateLimit-Limit',
        'X-RateLimit-Remaining', 
        'X-RateLimit-Reset',
        'Retry-After',
        'X-Rate-Limit-Limit',
        'X-Rate-Limit-Remaining',
        'X-Rate-Limit-Reset'
    ]
    
    found_headers = {}
    for header in rate_headers:
        if header in response.headers:
            found_headers[header] = response.headers[header]
    
    if found_headers:
        logger.info("   📊 Rate limit headers found:")
        for header, value in found_headers.items():
            logger.info(f"     {header}: {value}")
    else:
        logger.info("   ℹ️  No standard rate limit headers found")


def check_api_status():
    """Check general J-Quants API status."""
    logger.info("🌐 Checking J-Quants API status...")
    
    try:
        # Try to access the base API URL
        response = requests.get("https://api.jquants.com/v1/", timeout=10)
        logger.info(f"   Base API status: {response.status_code}")
        
        if response.status_code == 404:
            logger.info("   ℹ️  404 is normal for base URL")
        elif response.status_code == 200:
            logger.info("   ✅ API is responding")
        else:
            logger.warning(f"   ⚠️  Unexpected response: {response.status_code}")
            
    except Exception as e:
        logger.error(f"   💥 API status check failed: {e}")


def main():
    """Run comprehensive rate limit diagnosis."""
    logger.info("🩺 J-QUANTS API RATE LIMIT DIAGNOSIS")
    logger.info("=" * 60)
    logger.info(f"Timestamp: {datetime.now()}")
    logger.info(f"Project: {Config.GOOGLE_CLOUD_PROJECT}")
    logger.info(f"Email: {Config.JQUANTS_EMAIL}")
    logger.info("=" * 60)
    
    # Load environment
    load_dotenv()
    
    # Validate config
    try:
        Config.validate()
        logger.info("✅ Configuration validated")
    except Exception as e:
        logger.error(f"❌ Configuration error: {e}")
        return 1
    
    # Run diagnostics
    check_api_status()
    logger.info("")
    
    test_direct_api_call()
    logger.info("")
    
    test_jquants_library()
    logger.info("")
    
    # Summary
    logger.info("=" * 60)
    logger.info("📋 DIAGNOSIS SUMMARY")
    logger.info("=" * 60)
    logger.info("If you see '429' errors above, the API is rate limiting.")
    logger.info("Recommended actions:")
    logger.info("1. Wait 1-6 hours for rate limits to reset")
    logger.info("2. Use emergency_ingestion.py with 30+ second delays")
    logger.info("3. Consider using different API credentials if available")
    logger.info("4. Contact J-Quants support if limits seem too aggressive")
    logger.info("=" * 60)
    
    return 0


if __name__ == "__main__":
    sys.exit(main())