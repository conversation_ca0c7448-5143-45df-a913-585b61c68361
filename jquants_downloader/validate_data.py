#!/usr/bin/env python3
"""Validate existing BigQuery data against J-Quants API."""

import os
import sys
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Tuple
import pandas as pd
from google.cloud import bigquery
from dotenv import load_dotenv

# Add the parent directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import Config
from enhanced_jquants_client import RateLimitedClient, ComprehensiveDataFetcher

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class DataValidator:
    """Validate BigQuery data against J-Quants API."""
    
    def __init__(self, bq_client: bigquery.Client, jquants_client: RateLimitedClient):
        """Initialize validator with clients."""
        self.bq_client = bq_client
        self.jquants_client = jquants_client
        self.fetcher = ComprehensiveDataFetcher(jquants_client)
        self.validation_results = {}
    
    def validate_companies(self) -> Dict[str, Any]:
        """Validate company data completeness and accuracy."""
        logger.info("Validating company data...")
        
        # Get BigQuery data
        query = f"""
        SELECT code, company_name, date, COUNT(*) as record_count
        FROM `{Config.get_bigquery_table_id('companies')}`
        GROUP BY code, company_name, date
        ORDER BY code
        """
        bq_df = self.bq_client.query(query).to_dataframe()
        
        # Get latest J-Quants data
        jq_df = self.fetcher.fetch_all_companies()
        
        # Compare counts
        bq_companies = set(bq_df['code'].unique())
        jq_companies = set(jq_df['Code'].unique())
        
        missing_in_bq = jq_companies - bq_companies
        extra_in_bq = bq_companies - jq_companies
        
        result = {
            "bq_total": len(bq_companies),
            "jq_total": len(jq_companies),
            "missing_in_bq": list(missing_in_bq),
            "extra_in_bq": list(extra_in_bq),
            "match_rate": len(bq_companies & jq_companies) / len(jq_companies) * 100
        }
        
        logger.info(f"Company validation: {result['match_rate']:.2f}% match rate")
        logger.info(f"Missing in BigQuery: {len(missing_in_bq)} companies")
        
        self.validation_results['companies'] = result
        return result
    
    def validate_price_sample(self, sample_codes: List[str], days: int = 30) -> Dict[str, Any]:
        """Validate price data for a sample of companies."""
        logger.info(f"Validating price data for {len(sample_codes)} sample companies...")
        
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        mismatches = []
        
        for code in sample_codes:
            # Get BigQuery data
            query = f"""
            SELECT date, open, high, low, close, volume
            FROM `{Config.get_bigquery_table_id('daily_prices')}`
            WHERE code = '{code}'
            AND date >= '{start_date.strftime('%Y-%m-%d')}'
            ORDER BY date DESC
            """
            
            try:
                bq_df = self.bq_client.query(query).to_dataframe()
                
                # Get J-Quants data
                jq_df = self.jquants_client.get_prices_daily_quotes(
                    code=code,
                    from_yyyymmdd=start_date.strftime('%Y%m%d'),
                    to_yyyymmdd=end_date.strftime('%Y%m%d')
                )
                
                if len(bq_df) != len(jq_df):
                    mismatches.append({
                        "code": code,
                        "bq_count": len(bq_df),
                        "jq_count": len(jq_df),
                        "type": "count_mismatch"
                    })
                
                # Compare prices (sample check)
                if not jq_df.empty and not bq_df.empty:
                    latest_bq = bq_df.iloc[0]
                    latest_jq = jq_df.iloc[0]
                    
                    if abs(float(latest_bq['close']) - float(latest_jq['Close'])) > 0.01:
                        mismatches.append({
                            "code": code,
                            "date": str(latest_bq['date']),
                            "bq_close": float(latest_bq['close']),
                            "jq_close": float(latest_jq['Close']),
                            "type": "price_mismatch"
                        })
                        
            except Exception as e:
                logger.error(f"Error validating {code}: {e}")
                mismatches.append({
                    "code": code,
                    "error": str(e),
                    "type": "error"
                })
        
        result = {
            "sample_size": len(sample_codes),
            "mismatches": mismatches,
            "error_rate": len(mismatches) / len(sample_codes) * 100
        }
        
        logger.info(f"Price validation: {result['error_rate']:.2f}% error rate")
        
        self.validation_results['prices'] = result
        return result
    
    def validate_data_freshness(self) -> Dict[str, Any]:
        """Check data freshness across all tables."""
        logger.info("Validating data freshness...")
        
        tables_to_check = ['companies', 'daily_prices', 'dividends']
        freshness_results = {}
        
        for table in tables_to_check:
            query = f"""
            SELECT MAX(date) as latest_date,
                   COUNT(DISTINCT code) as unique_codes,
                   COUNT(*) as total_records
            FROM `{Config.get_bigquery_table_id(table)}`
            """
            
            try:
                result = list(self.bq_client.query(query))[0]
                
                days_old = (datetime.now().date() - result.latest_date).days if result.latest_date else None
                
                freshness_results[table] = {
                    "latest_date": str(result.latest_date) if result.latest_date else None,
                    "days_old": days_old,
                    "unique_codes": result.unique_codes,
                    "total_records": result.total_records,
                    "is_stale": days_old > 7 if days_old is not None else True
                }
                
            except Exception as e:
                logger.error(f"Error checking freshness for {table}: {e}")
                freshness_results[table] = {"error": str(e)}
        
        self.validation_results['freshness'] = freshness_results
        return freshness_results
    
    def generate_report(self) -> str:
        """Generate validation report."""
        report = []
        report.append("=" * 60)
        report.append("J-QUANTS DATA VALIDATION REPORT")
        report.append("=" * 60)
        report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # Company validation
        if 'companies' in self.validation_results:
            comp = self.validation_results['companies']
            report.append("📊 COMPANY DATA VALIDATION")
            report.append(f"  BigQuery Companies: {comp['bq_total']}")
            report.append(f"  J-Quants Companies: {comp['jq_total']}")
            report.append(f"  Match Rate: {comp['match_rate']:.2f}%")
            report.append(f"  Missing in BigQuery: {len(comp['missing_in_bq'])}")
            if comp['missing_in_bq']:
                report.append(f"  Sample Missing: {comp['missing_in_bq'][:5]}")
            report.append("")
        
        # Price validation
        if 'prices' in self.validation_results:
            prices = self.validation_results['prices']
            report.append("📈 PRICE DATA VALIDATION")
            report.append(f"  Sample Size: {prices['sample_size']} companies")
            report.append(f"  Error Rate: {prices['error_rate']:.2f}%")
            report.append(f"  Mismatches Found: {len(prices['mismatches'])}")
            report.append("")
        
        # Freshness validation
        if 'freshness' in self.validation_results:
            report.append("🕐 DATA FRESHNESS")
            for table, info in self.validation_results['freshness'].items():
                if 'error' not in info:
                    status = "⚠️ STALE" if info['is_stale'] else "✅ FRESH"
                    report.append(f"  {table}: {info['latest_date']} ({info['days_old']} days old) {status}")
                    report.append(f"    Records: {info['total_records']:,}")
                else:
                    report.append(f"  {table}: ❌ ERROR - {info['error']}")
            report.append("")
        
        report.append("=" * 60)
        
        return "\n".join(report)


def main():
    """Run data validation."""
    # Load environment variables
    load_dotenv()
    
    # Validate configuration
    try:
        Config.validate()
    except ValueError as e:
        logger.error(f"Configuration error: {e}")
        return 1
    
    # Initialize clients
    try:
        # BigQuery client
        bq_client = bigquery.Client(project=Config.GOOGLE_CLOUD_PROJECT)
        
        # J-Quants client with conservative rate limiting
        jq_client = RateLimitedClient(
            email=Config.JQUANTS_EMAIL,
            password=Config.JQUANTS_PASSWORD,
            calls_per_minute=20
        )
        
        # Create validator
        validator = DataValidator(bq_client, jq_client)
        
        # Run validations
        logger.info("Starting data validation...")
        
        # 1. Validate companies
        validator.validate_companies()
        
        # 2. Validate sample prices
        sample_codes = ["86970", "67580", "65020", "79740", "61780"]  # Sony, Nintendo, Toshiba, etc.
        validator.validate_price_sample(sample_codes)
        
        # 3. Check data freshness
        validator.validate_data_freshness()
        
        # Generate and print report
        report = validator.generate_report()
        print("\n" + report)
        
        # Save report
        report_path = f"validation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(report_path, 'w') as f:
            f.write(report)
        logger.info(f"Report saved to {report_path}")
        
        return 0
        
    except Exception as e:
        logger.error(f"Validation failed: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())