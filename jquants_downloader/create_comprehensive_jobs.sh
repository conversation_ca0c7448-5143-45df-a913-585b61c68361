#!/bin/bash

# Create comprehensive J-Quants ingestion jobs for all data types

set -e

PROJECT_ID="tokyotickers"
REGION="asia-northeast1"
SERVICE_ACCOUNT="jquants-service@${PROJECT_ID}.iam.gserviceaccount.com"

echo "🚀 Creating comprehensive J-Quants ingestion jobs"
echo "Project: $PROJECT_ID"
echo "Region: $REGION"
echo ""

# Set project
gcloud config set project $PROJECT_ID

# Job configurations
declare -A JOBS=(
    ["jquants-dividends"]="dividends-only"
    ["jquants-margin-trading"]="margin-trading-only"
    ["jquants-topix"]="topix-only"
    ["jquants-indices"]="indices-only"
    ["jquants-trading-by-type"]="trading-by-type-only"
    ["jquants-short-selling"]="short-selling-only"
    ["jquants-breakdown-trading"]="breakdown-trading-only"
    ["jquants-financial-detailed"]="financial-statements-detailed-only"
    ["jquants-futures"]="futures-only"
    ["jquants-options"]="options-only"
    ["jquants-index-options"]="index-options-only"
    ["jquants-comprehensive"]="comprehensive-backfill"
    ["jquants-ultimate-premium"]="ultimate-premium-backfill"
)

# Create each job
for job_name in "${!JOBS[@]}"; do
    mode="${JOBS[$job_name]}"

    echo "📋 Creating job: $job_name (mode: $mode)"

    # Set timeout based on job type
    if [[ "$mode" == "ultimate-premium-backfill" ]]; then
        timeout="259200"  # 72 hours
        memory="16Gi"
        cpu="8"
    elif [[ "$mode" == "comprehensive-backfill" ]]; then
        timeout="86400"  # 24 hours
        memory="8Gi"
        cpu="4"
    elif [[ "$mode" == "trading-by-type-only" || "$mode" == "short-selling-only" || "$mode" == "breakdown-trading-only" || "$mode" == "financial-statements-detailed-only" ]]; then
        timeout="14400"  # 4 hours
        memory="8Gi"
        cpu="4"
    elif [[ "$mode" == "futures-only" || "$mode" == "options-only" || "$mode" == "index-options-only" ]]; then
        timeout="10800"  # 3 hours
        memory="6Gi"
        cpu="3"
    elif [[ "$mode" == "dividends-only" || "$mode" == "margin-trading-only" ]]; then
        timeout="7200"   # 2 hours
        memory="4Gi"
        cpu="2"
    else
        timeout="3600"   # 1 hour
        memory="2Gi"
        cpu="1"
    fi

    gcloud run jobs create $job_name \
        --image gcr.io/$PROJECT_ID/jquants-ingestion \
        --region $REGION \
        --service-account $SERVICE_ACCOUNT \
        --memory $memory \
        --cpu $cpu \
        --max-retries 0 \
        --parallelism 1 \
        --task-timeout $timeout \
        --set-env-vars "GOOGLE_CLOUD_PROJECT=$PROJECT_ID" \
        --set-env-vars "BIGQUERY_DATASET=jquants_data" \
        --set-env-vars "JQUANTS_EMAIL=<EMAIL>" \
        --set-env-vars "JQUANTS_PASSWORD=Borboletas747" \
        --command "python" \
        --args "production_ingestion_service.py,--mode,$mode" \
        || echo "Job $job_name already exists"

    echo "✅ Job $job_name created/updated"
    echo ""
done

echo "🎉 All jobs created successfully!"
echo ""
echo "📋 Available jobs:"
echo "  jquants-backfill              - Historical prices only"
echo "  jquants-dividends             - Dividend data (2013-2025)"
echo "  jquants-margin-trading        - Margin trading data (2012-2025)"
echo "  jquants-topix                 - TOPIX index data (2008-2025)"
echo "  jquants-indices               - Other indices data (2008-2025)"
echo "  jquants-trading-by-type       - Trading by investor type (2008-2025)"
echo "  jquants-short-selling         - Short selling by sector (2008-2025)"
echo "  jquants-breakdown-trading     - Breakdown trading data (2015-2025)"
echo "  jquants-financial-detailed    - Detailed financial statements (2009-2025)"
echo "  jquants-futures               - Futures data (2008-2025)"
echo "  jquants-options               - Options data (2008-2025)"
echo "  jquants-index-options         - Index options data (2008-2025)"
echo "  jquants-comprehensive         - Basic data types (24+ hours)"
echo "  jquants-ultimate-premium      - ALL PREMIUM DATA (72+ hours)"
echo ""
echo "🚀 To run a specific job:"
echo "  gcloud run jobs execute <job-name> --region $REGION"
echo ""
echo "🎯 To run the ULTIMATE PREMIUM backfill (ALL DATA):"
echo "  gcloud run jobs execute jquants-ultimate-premium --region $REGION"
echo ""
echo "⚠️  WARNING: The ultimate premium job will take 72+ hours and download:"
echo "   - 4,411 companies"
echo "   - ~18M daily price records (2008-2025)"
echo "   - ~500K financial statement records"
echo "   - ~2M dividend records (2013-2025)"
echo "   - ~500K margin trading records (2012-2025)"
echo "   - ~4K TOPIX records (2008-2025)"
echo "   - ~50K other indices records (2008-2025)"
echo "   - ~1M trading by type records (2008-2025)"
echo "   - ~500K short selling records (2008-2025)"
echo "   - ~200K breakdown trading records (2015-2025)"
echo "   - ~2M detailed financial statements (2009-2025)"
echo "   - ~5M futures records (2008-2025)"
echo "   - ~10M options records (2008-2025)"
echo "   - ~1M index options records (2008-2025)"
echo "   - TOTAL: ~40M+ records (COMPLETE JAPANESE MARKET DATABASE)"
