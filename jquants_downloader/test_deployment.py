#!/usr/bin/env python3
# ABOUTME: Test script to check which version of code is deployed
# ABOUTME: Checks the /status endpoint and looks for version info

import subprocess
import requests
import json

def get_auth_token():
    """Get authentication token."""
    try:
        result = subprocess.run(
            ['gcloud', 'auth', 'print-identity-token'],
            capture_output=True,
            text=True,
            timeout=10
        )
        if result.returncode == 0:
            return result.stdout.strip()
    except Exception as e:
        print(f"Error getting auth token: {e}")
    return None

def check_deployment():
    """Check deployment status and version."""
    base_url = "https://jquants-ingestion-621634133093.asia-northeast1.run.app"
    
    print("🔍 Checking Cloud Run deployment...")
    
    # Get service info
    result = subprocess.run([
        'gcloud', 'run', 'services', 'describe', 'jquants-ingestion',
        '--region=asia-northeast1', '--format=json'
    ], capture_output=True, text=True)
    
    if result.returncode == 0:
        service_info = json.loads(result.stdout)
        revision = service_info.get('status', {}).get('latestReadyRevisionName', 'unknown')
        print(f"✅ Current revision: {revision}")
        
        # Check when it was created
        created = service_info.get('status', {}).get('conditions', [{}])[0].get('lastTransitionTime', 'unknown')
        print(f"   Last updated: {created}")
    else:
        print("❌ Could not get service info")
    
    # Check if the service is responding
    token = get_auth_token()
    if token:
        try:
            response = requests.get(
                f"{base_url}/",
                headers={'Authorization': f'Bearer {token}'},
                timeout=10
            )
            print(f"\n📡 Service health check: {response.status_code}")
        except Exception as e:
            print(f"❌ Health check failed: {e}")
    
    # Check recent logs for errors
    print("\n📋 Recent deployment logs:")
    logs_result = subprocess.run([
        'gcloud', 'run', 'services', 'logs', 'read', 'jquants-ingestion',
        '--region=asia-northeast1', '--limit=5', '--format=value(textPayload)'
    ], capture_output=True, text=True)
    
    if logs_result.returncode == 0:
        for line in logs_result.stdout.strip().split('\n')[:5]:
            if line:
                print(f"   {line[:100]}")

if __name__ == "__main__":
    check_deployment()