#!/usr/bin/env python3
"""
BigQuery Data Ingestion Monitor

This script provides comprehensive monitoring of J-Quants data ingestion into BigQuery.
It shows table statistics, data freshness, ingestion rates, and job history.
"""

import argparse
from datetime import datetime, timedelta
from google.cloud import bigquery
from tabulate import tabulate
import pandas as pd

class BigQueryMonitor:
    def __init__(self, project_id='tokyotickers', dataset_id='jquants_data'):
        self.client = bigquery.Client(project=project_id)
        self.project_id = project_id
        self.dataset_id = dataset_id
        
    def get_table_stats(self):
        """Get comprehensive statistics for all tables in the dataset."""
        query = f"""
        WITH table_stats AS (
            SELECT 
                table_id as table_name,
                ROUND(size_bytes / POW(10, 9), 2) as size_gb,
                row_count,
                ROUND(size_bytes / POW(10, 6), 2) as size_mb,
                creation_time,
                TIMESTAMP_DIFF(CURRENT_TIMESTAMP(), creation_time, DAY) as days_old
            FROM `{self.project_id}.{self.dataset_id}.__TABLES__`
        ),
        latest_dates AS (
            SELECT 'daily_prices' as table_name, MAX(date) as latest_date 
            FROM `{self.project_id}.{self.dataset_id}.daily_prices`
            UNION ALL
            SELECT 'companies', MAX(Date) FROM `{self.project_id}.{self.dataset_id}.companies`
            UNION ALL
            SELECT 'dividends', MAX(AnnouncementDate) FROM `{self.project_id}.{self.dataset_id}.dividends`
            UNION ALL
            SELECT 'financial_statements', MAX(disclosure_date) FROM `{self.project_id}.{self.dataset_id}.financial_statements`
            UNION ALL
            SELECT 'margin_trading', MAX(Date) FROM `{self.project_id}.{self.dataset_id}.margin_trading`
            UNION ALL
            SELECT 'topix_data', MAX(Date) FROM `{self.project_id}.{self.dataset_id}.topix_data`
        )
        SELECT 
            t.table_name,
            t.row_count,
            t.size_mb,
            t.size_gb,
            CAST(l.latest_date AS STRING) as latest_data_date,
            TIMESTAMP_DIFF(CURRENT_TIMESTAMP(), CAST(l.latest_date AS TIMESTAMP), DAY) as days_since_update
        FROM table_stats t
        LEFT JOIN latest_dates l ON t.table_name = l.table_name
        ORDER BY t.row_count DESC
        """
        
        return self.client.query(query).to_dataframe()
    
    def get_ingestion_jobs(self, hours=24):
        """Get recent BigQuery load jobs."""
        query = f"""
        SELECT 
            TIMESTAMP_TRUNC(creation_time, MINUTE) as job_time,
            job_id,
            REGEXP_EXTRACT(job_id, r'load_job_(.+?)_') as table_hint,
            destination_table.table_id as table_name,
            CASE 
                WHEN error_result IS NULL THEN 'SUCCESS'
                ELSE 'FAILED'
            END as status,
            total_bytes_processed / POW(10, 6) as mb_processed,
            total_slot_ms / 1000 as slot_seconds,
            error_result.message as error_message
        FROM `{self.project_id}.region-asia-northeast1.INFORMATION_SCHEMA.JOBS_BY_PROJECT`
        WHERE creation_time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL {hours} HOUR)
            AND job_type = 'LOAD'
            AND destination_table.dataset_id = '{self.dataset_id}'
        ORDER BY creation_time DESC
        LIMIT 50
        """
        
        return self.client.query(query).to_dataframe()
    
    def get_ingestion_rate(self, table_name, hours=24):
        """Calculate ingestion rate for a specific table."""
        # Map table to its timestamp column
        timestamp_columns = {
            'daily_prices': 'ingestion_timestamp',
            'companies': 'ingestion_timestamp', 
            'dividends': 'ingestion_timestamp',
            'financial_statements': 'ingestion_timestamp',
            'margin_trading': 'ingestion_timestamp',
            'topix_data': 'ingestion_timestamp'
        }
        
        if table_name not in timestamp_columns:
            return None
            
        ts_col = timestamp_columns[table_name]
        
        query = f"""
        WITH hourly_counts AS (
            SELECT 
                TIMESTAMP_TRUNC({ts_col}, HOUR) as hour,
                COUNT(*) as records_ingested
            FROM `{self.project_id}.{self.dataset_id}.{table_name}`
            WHERE {ts_col} >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL {hours} HOUR)
            GROUP BY hour
        )
        SELECT 
            MIN(hour) as first_ingestion,
            MAX(hour) as last_ingestion,
            SUM(records_ingested) as total_records,
            AVG(records_ingested) as avg_per_hour,
            MAX(records_ingested) as max_per_hour
        FROM hourly_counts
        """
        
        try:
            return self.client.query(query).to_dataframe()
        except:
            return None
    
    def get_data_coverage(self):
        """Check data coverage across different tables."""
        query = f"""
        WITH company_counts AS (
            SELECT COUNT(DISTINCT Code) as total_companies 
            FROM `{self.project_id}.{self.dataset_id}.companies`
        ),
        price_coverage AS (
            SELECT COUNT(DISTINCT code) as companies_with_prices
            FROM `{self.project_id}.{self.dataset_id}.daily_prices`
        ),
        dividend_coverage AS (
            SELECT COUNT(DISTINCT Code) as companies_with_dividends
            FROM `{self.project_id}.{self.dataset_id}.dividends`
        ),
        financial_coverage AS (
            SELECT COUNT(DISTINCT code) as companies_with_financials
            FROM `{self.project_id}.{self.dataset_id}.financial_statements`
        )
        SELECT 
            c.total_companies,
            p.companies_with_prices,
            ROUND(p.companies_with_prices / c.total_companies * 100, 1) as price_coverage_pct,
            d.companies_with_dividends,
            ROUND(d.companies_with_dividends / c.total_companies * 100, 1) as dividend_coverage_pct,
            f.companies_with_financials,
            ROUND(f.companies_with_financials / c.total_companies * 100, 1) as financial_coverage_pct
        FROM company_counts c
        CROSS JOIN price_coverage p
        CROSS JOIN dividend_coverage d
        CROSS JOIN financial_coverage f
        """
        
        return self.client.query(query).to_dataframe()
    
    def print_report(self, verbose=False):
        """Print comprehensive monitoring report."""
        print("\n" + "="*80)
        print("📊 BIGQUERY DATA INGESTION MONITOR")
        print("="*80)
        print(f"Project: {self.project_id}")
        print(f"Dataset: {self.dataset_id}")
        print(f"Report Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Table Statistics
        print("\n📋 TABLE STATISTICS:")
        print("-"*80)
        stats_df = self.get_table_stats()
        if not stats_df.empty:
            print(tabulate(stats_df, headers='keys', tablefmt='grid', floatfmt=".2f"))
        
        # Data Coverage
        print("\n📈 DATA COVERAGE:")
        print("-"*80)
        coverage_df = self.get_data_coverage()
        if not coverage_df.empty:
            for _, row in coverage_df.iterrows():
                print(f"Total Companies: {row['total_companies']:,}")
                print(f"Companies with Prices: {row['companies_with_prices']:,} ({row['price_coverage_pct']}%)")
                print(f"Companies with Dividends: {row['companies_with_dividends']:,} ({row['dividend_coverage_pct']}%)")
                print(f"Companies with Financials: {row['companies_with_financials']:,} ({row['financial_coverage_pct']}%)")
        
        # Recent Jobs
        print("\n💼 RECENT INGESTION JOBS (Last 24 Hours):")
        print("-"*80)
        jobs_df = self.get_ingestion_jobs()
        if not jobs_df.empty:
            summary = jobs_df.groupby('status').size()
            print(f"Success: {summary.get('SUCCESS', 0)} | Failed: {summary.get('FAILED', 0)}")
            
            # Show failed jobs
            failed = jobs_df[jobs_df['status'] == 'FAILED']
            if not failed.empty:
                print("\n❌ FAILED JOBS:")
                for _, job in failed.iterrows():
                    print(f"  - {job['job_time']} | {job['table_name']} | {job['error_message']}")
        
        # Ingestion Rates (if verbose)
        if verbose:
            print("\n⚡ INGESTION RATES (Last 24 Hours):")
            print("-"*80)
            for table in ['daily_prices', 'dividends', 'financial_statements']:
                rate_df = self.get_ingestion_rate(table)
                if rate_df is not None and not rate_df.empty:
                    row = rate_df.iloc[0]
                    if pd.notna(row['total_records']):
                        print(f"\n{table}:")
                        print(f"  Total Records: {int(row['total_records']):,}")
                        print(f"  Avg Per Hour: {int(row['avg_per_hour']):,}")
                        print(f"  Max Per Hour: {int(row['max_per_hour']):,}")
                        print(f"  Last Update: {row['last_ingestion']}")
        
        print("\n" + "="*80)
        print("✅ Report Complete")
        print("="*80)
    
    def get_monitoring_query(self):
        """Return the main monitoring query for manual execution."""
        return f"""
-- BigQuery Data Ingestion Monitor Query
-- Run this in BigQuery Console for real-time monitoring

WITH table_stats AS (
    SELECT 
        table_name,
        ROUND(size_bytes / POW(10, 9), 2) as size_gb,
        row_count,
        creation_time,
        TIMESTAMP_DIFF(CURRENT_TIMESTAMP(), creation_time, DAY) as days_old
    FROM `{self.project_id}.{self.dataset_id}.__TABLES__`
),
latest_dates AS (
    SELECT 'daily_prices' as table_name, MAX(date) as latest_date, COUNT(*) as record_count 
    FROM `{self.project_id}.{self.dataset_id}.daily_prices`
    UNION ALL
    SELECT 'companies', MAX(Date), COUNT(*) FROM `{self.project_id}.{self.dataset_id}.companies`
    UNION ALL
    SELECT 'dividends', MAX(AnnouncementDate), COUNT(*) FROM `{self.project_id}.{self.dataset_id}.dividends`
    UNION ALL
    SELECT 'financial_statements', MAX(disclosure_date), COUNT(*) FROM `{self.project_id}.{self.dataset_id}.financial_statements`
),
recent_jobs AS (
    SELECT 
        destination_table.table_id as table_name,
        COUNT(*) as job_count,
        SUM(CASE WHEN error_result IS NULL THEN 1 ELSE 0 END) as successful_jobs,
        SUM(CASE WHEN error_result IS NOT NULL THEN 1 ELSE 0 END) as failed_jobs,
        MAX(creation_time) as last_job_time
    FROM `{self.project_id}.region-asia-northeast1.INFORMATION_SCHEMA.JOBS_BY_PROJECT`
    WHERE creation_time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 24 HOUR)
        AND job_type = 'LOAD'
        AND destination_table.dataset_id = '{self.dataset_id}'
    GROUP BY table_name
)
SELECT 
    t.table_name,
    t.row_count,
    t.size_gb,
    CAST(l.latest_date AS STRING) as latest_data_date,
    TIMESTAMP_DIFF(CURRENT_TIMESTAMP(), CAST(l.latest_date AS TIMESTAMP), HOUR) as hours_since_update,
    j.job_count as jobs_24h,
    j.successful_jobs as success_24h,
    j.failed_jobs as failed_24h,
    j.last_job_time
FROM table_stats t
LEFT JOIN latest_dates l ON t.table_name = l.table_name
LEFT JOIN recent_jobs j ON t.table_name = j.table_name
ORDER BY t.row_count DESC;
"""

def main():
    parser = argparse.ArgumentParser(description='Monitor BigQuery data ingestion')
    parser.add_argument('-v', '--verbose', action='store_true', help='Show detailed ingestion rates')
    parser.add_argument('--query-only', action='store_true', help='Print monitoring query only')
    parser.add_argument('--project', default='tokyotickers', help='GCP project ID')
    parser.add_argument('--dataset', default='jquants_data', help='BigQuery dataset ID')
    
    args = parser.parse_args()
    
    monitor = BigQueryMonitor(project_id=args.project, dataset_id=args.dataset)
    
    if args.query_only:
        print(monitor.get_monitoring_query())
    else:
        monitor.print_report(verbose=args.verbose)

if __name__ == "__main__":
    main()