"""Unit tests for data models."""

import pytest
from datetime import date
from models import (
    ListedCompany, DailyPrice, FinancialStatement, 
    Dividend, TopixData, TradingByType, MarginTrading
)


class TestListedCompany:
    """Test ListedCompany model."""
    
    def test_listed_company_creation(self):
        """Test creating a ListedCompany instance."""
        company = ListedCompany(
            date=date(2024, 1, 1),
            code="86970",
            company_name="ソニーグループ",
            company_name_english="Sony Group Corporation",
            sector17_code="16",
            sector17_code_name="電気機器",
            market_code="0111",
            market_code_name="プライム"
        )
        
        assert company.code == "86970"
        assert company.company_name == "ソニーグループ"
        assert company.date == date(2024, 1, 1)
    
    def test_listed_company_optional_fields(self):
        """Test ListedCompany with minimal required fields."""
        company = ListedCompany(
            date=date(2024, 1, 1),
            code="86970",
            company_name="ソニーグループ"
        )
        
        assert company.company_name_english is None
        assert company.margin_code is None


class TestDailyPrice:
    """Test DailyPrice model."""
    
    def test_daily_price_creation(self):
        """Test creating a DailyPrice instance."""
        price = DailyPrice(
            date=date(2024, 1, 10),
            code="86970",
            open=3000.0,
            high=3050.0,
            low=2980.0,
            close=3020.0,
            volume=1000000.0,
            turnover_value=3020000000.0,
            adjustment_factor=1.0,
            adjustment_close=3020.0
        )
        
        assert price.code == "86970"
        assert price.open == 3000.0
        assert price.close == 3020.0
        assert price.volume == 1000000.0
    
    def test_daily_price_premium_fields(self):
        """Test DailyPrice with premium plan fields."""
        price = DailyPrice(
            date=date(2024, 1, 10),
            code="86970",
            morning_open=3000.0,
            morning_close=3010.0,
            afternoon_open=3015.0,
            afternoon_close=3020.0
        )
        
        assert price.morning_open == 3000.0
        assert price.afternoon_close == 3020.0


class TestFinancialStatement:
    """Test FinancialStatement model."""
    
    def test_financial_statement_creation(self):
        """Test creating a FinancialStatement instance."""
        statement = FinancialStatement(
            disclosed_date=date(2024, 2, 8),
            disclosed_time="15:00:00",
            local_code="86970",
            type_of_current_period="3Q",
            net_sales="2700000000000",
            operating_profit="416000000000",
            ordinary_profit="425000000000",
            profit="310000000000",
            earnings_per_share="250.50"
        )
        
        assert statement.local_code == "86970"
        assert statement.type_of_current_period == "3Q"
        assert statement.net_sales == "2700000000000"
        assert statement.earnings_per_share == "250.50"


class TestDividend:
    """Test Dividend model."""
    
    def test_dividend_creation(self):
        """Test creating a Dividend instance."""
        dividend = Dividend(
            announcement_date=date(2024, 2, 8),
            code="86970",
            gross_dividend_rate="35.00",
            record_date=date(2024, 3, 31),
            ex_date=date(2024, 3, 28),
            payable_date=date(2024, 6, 1),
            interim_final_code="2",
            forecast_result_code="1"
        )
        
        assert dividend.code == "86970"
        assert dividend.gross_dividend_rate == "35.00"
        assert dividend.interim_final_code == "2"  # Final dividend
        assert dividend.forecast_result_code == "1"  # Result (not forecast)


class TestTopixData:
    """Test TopixData model."""
    
    def test_topix_data_creation(self):
        """Test creating a TopixData instance."""
        topix = TopixData(
            date=date(2024, 1, 10),
            open=2400.50,
            high=2420.75,
            low=2395.25,
            close=2415.00
        )
        
        assert topix.date == date(2024, 1, 10)
        assert topix.open == 2400.50
        assert topix.close == 2415.00


class TestTradingByType:
    """Test TradingByType model."""
    
    def test_trading_by_type_creation(self):
        """Test creating a TradingByType instance."""
        trading = TradingByType(
            date=date(2024, 1, 10),
            section="TSEPrime",
            proprietary_total=123456789.0,
            individual_total=987654321.0,
            foreigner_total=555555555.0
        )
        
        assert trading.section == "TSEPrime"
        assert trading.individual_total == 987654321.0
        assert trading.foreigner_total == 555555555.0


class TestMarginTrading:
    """Test MarginTrading model."""
    
    def test_margin_trading_creation(self):
        """Test creating a MarginTrading instance."""
        margin = MarginTrading(
            date=date(2024, 1, 10),
            code="86970",
            long_sell_value=1000000.0,
            short_sell_without_margin_value=500000.0,
            margin_sell_new_value=300000.0,
            margin_buy_new_value=800000.0
        )
        
        assert margin.code == "86970"
        assert margin.long_sell_value == 1000000.0
        assert margin.margin_buy_new_value == 800000.0


if __name__ == "__main__":
    pytest.main([__file__, "-v"])