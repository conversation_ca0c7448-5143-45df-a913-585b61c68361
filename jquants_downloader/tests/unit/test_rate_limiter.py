"""Unit tests for rate limiter."""

import pytest
import time
from jquants_client_v2 import RateLimiter


class TestRateLimiter:
    """Test RateLimiter functionality."""
    
    def test_rate_limiter_initialization(self):
        """Test RateLimiter initialization."""
        limiter = RateLimiter(calls_per_minute=60)
        assert limiter.calls_per_minute == 60
        assert limiter.min_interval == 1.0  # 60 calls/minute = 1 call/second
        
    def test_rate_limiter_delay(self):
        """Test that RateLimiter enforces delays."""
        limiter = RateLimiter(calls_per_minute=120)  # 2 calls per second
        
        # First call should not wait
        start_time = time.time()
        limiter.wait_if_needed()
        first_call_time = time.time() - start_time
        assert first_call_time < 0.1  # Should be immediate
        
        # Second call should wait if called too quickly
        start_time = time.time()
        limiter.wait_if_needed()
        second_call_time = time.time() - start_time
        assert second_call_time >= 0.4  # Should wait ~0.5 seconds
        
    def test_rate_limiter_multiple_calls(self):
        """Test rate limiter with multiple rapid calls."""
        limiter = RateLimiter(calls_per_minute=30)  # 0.5 calls per second = 2 seconds between calls
        
        call_times = []
        for i in range(3):
            start_time = time.time()
            limiter.wait_if_needed()
            call_times.append(time.time())
        
        # Check intervals between calls
        interval1 = call_times[1] - call_times[0]
        interval2 = call_times[2] - call_times[1]
        
        assert interval1 >= 1.9  # Should be ~2 seconds
        assert interval2 >= 1.9  # Should be ~2 seconds


if __name__ == "__main__":
    pytest.main([__file__, "-v"])