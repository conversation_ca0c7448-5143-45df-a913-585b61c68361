#!/usr/bin/env python3
"""Test the refresh token directly."""

import requests
import json
from dotenv import load_dotenv
import os

def test_refresh_token():
    """Test the refresh token directly."""
    load_dotenv()

    refresh_token = os.getenv("JQUANTS_REFRESH_TOKEN")
    if not refresh_token:
        print("❌ No refresh token found in .env")
        return

    # Remove quotes if present
    refresh_token = refresh_token.strip('"\'')

    print(f"Testing refresh token: {refresh_token[:50]}...")

    url = "https://api.jquants.com/v1/token/auth_refresh"

    data = {
        "refreshtoken": refresh_token
    }

    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }

    try:
        print(f"Sending data: {data}")
        response = requests.post(url, json=data, headers=headers)
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        print(f"Response Text: {response.text}")

        if response.status_code == 200:
            result = response.json()
            if "idToken" in result:
                print("✅ Successfully got ID token!")
                print(f"ID Token: {result['idToken'][:50]}...")
            else:
                print("❌ No ID token in response")
                print(f"Response: {result}")
        else:
            print("❌ Request failed")
            try:
                error_detail = response.json()
                print(f"Error details: {error_detail}")
            except:
                print(f"Error response: {response.text}")

        # Try with different content type
        print("\n--- Trying with form data ---")
        headers2 = {
            "Content-Type": "application/x-www-form-urlencoded",
            "Accept": "application/json"
        }

        response2 = requests.post(url, data=data, headers=headers2)
        print(f"Status Code: {response2.status_code}")
        print(f"Response Text: {response2.text}")

    except Exception as e:
        print(f"❌ Exception: {e}")

if __name__ == "__main__":
    test_refresh_token()
