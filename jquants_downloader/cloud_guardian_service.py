#!/usr/bin/env python3
# ABOUTME: Cloud-based guardian service that runs as a long-lived Cloud Run service
# ABOUTME: Monitors ingestion progress and triggers when needed, designed for Cloud Run

from flask import Flask, jsonify
import threading
import time
import json
import requests
from datetime import datetime, timedelta
import logging
import os
from google.cloud import bigquery

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = Flask(__name__)

class CloudGuardian:
    def __init__(self):
        self.service_url = os.environ.get('INGESTION_SERVICE_URL', 
                                         'https://jquants-ingestion-621634133093.asia-northeast1.run.app')
        self.project_id = os.environ.get('GCP_PROJECT', 'tokyotickers')
        self.dataset_id = 'jquants_data'
        self.last_progress_time = None
        self.last_record_count = 0
        self.consecutive_idle_checks = 0
        self.max_idle_minutes = int(os.environ.get('MAX_IDLE_MINUTES', '15'))
        self.check_interval = int(os.environ.get('CHECK_INTERVAL_SECONDS', '300'))
        self.is_running = False
        self.guardian_thread = None
        self.status = {'status': 'initialized', 'last_check': None}
        
        # Initialize BigQuery client
        self.bigquery_client = bigquery.Client(project=self.project_id)
        
    def get_auth_token(self):
        """Get GCP authentication token using metadata service"""
        try:
            # In Cloud Run, use metadata service
            metadata_url = 'http://metadata.google.internal/computeMetadata/v1/instance/service-accounts/default/identity'
            headers = {'Metadata-Flavor': 'Google'}
            params = {'audience': self.service_url}
            
            response = requests.get(metadata_url, headers=headers, params=params, timeout=10)
            if response.status_code == 200:
                return response.text
            else:
                logger.error(f"Failed to get token from metadata service: {response.status_code}")
                return None
        except Exception as e:
            logger.error(f"Failed to get auth token: {e}")
            return None
            
    def get_table_counts(self):
        """Get current record counts from BigQuery"""
        tables = [
            'companies', 'daily_prices', 'dividends', 'financial_statements',
            'margin_trading', 'topix_data', 'indices_data', 'trading_by_type',
            'short_selling', 'breakdown_trading', 'financial_statements_detailed',
            'futures', 'options', 'index_options'
        ]
        
        total_records = 0
        table_counts = {}
        
        for table in tables:
            try:
                query = f"SELECT COUNT(*) as count FROM `{self.project_id}.{self.dataset_id}.{table}`"
                result = list(self.bigquery_client.query(query).result())
                count = result[0]['count'] if result else 0
                table_counts[table] = count
                total_records += count
            except Exception as e:
                logger.warning(f"Failed to count {table}: {e}")
                table_counts[table] = 0
                
        return total_records, table_counts
        
    def trigger_ingestion(self, mode='daily-update'):
        """Trigger the ingestion service"""
        try:
            token = self.get_auth_token()
            if not token:
                logger.error("No auth token available")
                return False
                
            headers = {
                'Authorization': f'Bearer {token}',
                'Content-Type': 'application/json'
            }
            
            logger.info(f"Triggering {mode} ingestion...")
            
            response = requests.post(
                f"{self.service_url}/trigger",
                headers=headers,
                json={'mode': mode},
                timeout=300
            )
            
            if response.status_code == 200:
                logger.info(f"Successfully triggered {mode} ingestion")
                return True
            else:
                logger.error(f"Failed to trigger: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to trigger ingestion: {e}")
            return False
            
    def guardian_loop(self):
        """Main guardian loop"""
        logger.info("Guardian loop started")
        self.is_running = True
        
        while self.is_running:
            try:
                # Get current status
                total_records, table_counts = self.get_table_counts()
                
                # Check for progress
                progress_made = False
                if total_records > self.last_record_count:
                    new_records = total_records - self.last_record_count
                    logger.info(f"Progress detected: {new_records:,} new records")
                    self.last_record_count = total_records
                    self.last_progress_time = datetime.now()
                    self.consecutive_idle_checks = 0
                    progress_made = True
                else:
                    self.consecutive_idle_checks += 1
                    idle_minutes = self.consecutive_idle_checks * (self.check_interval / 60)
                    logger.info(f"No progress for {idle_minutes:.0f} minutes")
                    
                # Update status
                self.status = {
                    'status': 'running',
                    'last_check': datetime.now().isoformat(),
                    'total_records': total_records,
                    'last_progress': self.last_progress_time.isoformat() if self.last_progress_time else None,
                    'consecutive_idle_checks': self.consecutive_idle_checks,
                    'idle_minutes': self.consecutive_idle_checks * (self.check_interval / 60)
                }
                
                # Trigger if idle too long
                idle_minutes = self.consecutive_idle_checks * (self.check_interval / 60)
                if idle_minutes >= self.max_idle_minutes:
                    logger.warning(f"No progress for {idle_minutes:.0f} minutes - triggering ingestion!")
                    
                    # Try different modes
                    modes = ['daily-update', 'dividends-only', 'prices-only']
                    for mode in modes:
                        if self.trigger_ingestion(mode):
                            self.consecutive_idle_checks = 0
                            break
                        time.sleep(5)
                        
                # Sleep until next check
                time.sleep(self.check_interval)
                
            except Exception as e:
                logger.error(f"Error in guardian loop: {e}")
                self.status['error'] = str(e)
                time.sleep(30)
                
        logger.info("Guardian loop stopped")
        
    def start(self):
        """Start the guardian thread"""
        if not self.is_running:
            self.guardian_thread = threading.Thread(target=self.guardian_loop)
            self.guardian_thread.daemon = True
            self.guardian_thread.start()
            return True
        return False
        
    def stop(self):
        """Stop the guardian thread"""
        self.is_running = False
        if self.guardian_thread:
            self.guardian_thread.join(timeout=10)
            
# Global guardian instance
guardian = CloudGuardian()

@app.route('/')
def index():
    return jsonify({
        'service': 'J-Quants Ingestion Guardian',
        'status': guardian.status,
        'config': {
            'max_idle_minutes': guardian.max_idle_minutes,
            'check_interval_seconds': guardian.check_interval,
            'ingestion_service': guardian.service_url
        }
    })
    
@app.route('/health')
def health():
    return jsonify({'status': 'healthy', 'guardian_running': guardian.is_running})
    
@app.route('/start', methods=['POST'])
def start_guardian():
    if guardian.start():
        return jsonify({'status': 'started'})
    else:
        return jsonify({'status': 'already running'}), 400
        
@app.route('/stop', methods=['POST'])
def stop_guardian():
    guardian.stop()
    return jsonify({'status': 'stopped'})
    
if __name__ == '__main__':
    # Auto-start guardian when service starts
    guardian.start()
    
    # Run Flask app
    port = int(os.environ.get('PORT', 8080))
    app.run(host='0.0.0.0', port=port)