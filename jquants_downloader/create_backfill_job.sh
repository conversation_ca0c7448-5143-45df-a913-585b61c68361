#!/bin/bash

# Create Cloud Run Job for full historical backfill
# This is a separate job because it takes 24-48 hours to complete

set -e

# Configuration
PROJECT_ID="tokyotickers"
REGION="asia-northeast1"
JOB_NAME="jquants-backfill"
SERVICE_ACCOUNT="<EMAIL>"

echo "🚀 Creating Cloud Run Job for full historical backfill"
echo "Project: $PROJECT_ID"
echo "Region: $REGION"
echo "Job: $JOB_NAME"
echo ""

# Set the project
gcloud config set project $PROJECT_ID

# Create Cloud Run Job for full backfill
echo "📋 Creating Cloud Run Job..."
gcloud run jobs create $JOB_NAME \
    --image gcr.io/$PROJECT_ID/jquants-ingestion \
    --region $REGION \
    --service-account $SERVICE_ACCOUNT \
    --memory 4Gi \
    --cpu 2 \
    --max-retries 0 \
    --parallelism 1 \
    --task-timeout 7200 \
    --set-env-vars "GOOGLE_CLOUD_PROJECT=$PROJECT_ID" \
    --set-env-vars "BIGQUERY_DATASET=jquants_data" \
    --set-env-vars "JQUANTS_EMAIL=<EMAIL>" \
    --set-env-vars "JQUANTS_PASSWORD=Borboletas747" \
    --command "python" \
    --args "production_ingestion_service.py,--mode,prices-only" \
    || echo "Job already exists"

echo ""
echo "✅ Cloud Run Job created!"
echo ""
echo "📋 To start the full historical backfill:"
echo "   gcloud run jobs execute $JOB_NAME --region $REGION"
echo ""
echo "⚠️  WARNING: This will take 24-48 hours and ingest ~18M records!"
echo "⚠️  Make sure you have sufficient BigQuery quota!"
echo ""
echo "📊 Monitor progress:"
echo "   gcloud run jobs executions list --job $JOB_NAME --region $REGION"
echo ""
echo "📋 Check logs:"
echo "   gcloud logs read --project $PROJECT_ID --filter 'resource.type=cloud_run_job'"
