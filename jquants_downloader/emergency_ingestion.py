#!/usr/bin/env python3
"""Emergency ultra-conservative ingestion service to handle rate limiting."""

import logging
import time
import sys
from datetime import datetime, timedelta
from typing import Dict, Any
import pandas as pd
from google.cloud import bigquery

# Add src to path
sys.path.insert(0, 'src')

from jquants_downloader.config import Config
from jquants_downloader.clients.ultra_conservative_client import UltraConservativeJQuantsClient
from jquants_downloader.clients.bigquery_client import BigQueryClient

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class EmergencyIngestionService:
    """Emergency ingestion service with ultra-conservative approach."""
    
    def __init__(self):
        """Initialize with ultra-conservative settings."""
        logger.info("🚨 Emergency Ingestion Service - Ultra Conservative Mode")
        
        self.jquants_client = UltraConservativeJQuantsClient(
            email=Config.JQUANTS_EMAIL,
            password=Config.JQUANTS_PASSWORD
        )
        
        self.bigquery_client = BigQueryClient(
            project_id=Config.GOOGLE_CLOUD_PROJECT,
            dataset_id=Config.BIGQUERY_DATASET
        )
        
        logger.info("✅ Emergency service initialized")
    
    def test_basic_connectivity(self) -> bool:
        """Test if we can make any API call at all."""
        logger.info("🔌 Testing basic API connectivity...")
        
        return self.jquants_client.test_connectivity()
    
    def emergency_dividend_sample(self) -> Dict[str, Any]:
        """Get a very small sample of dividend data."""
        logger.info("💰 Emergency dividend sample - single day only")
        
        try:
            # Try to get data for just today
            today = datetime.now().strftime('%Y-%m-%d')
            yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
            
            logger.info(f"📅 Attempting to get dividend data for {yesterday}")
            
            # Try yesterday first (more likely to have data)
            dividends = self.jquants_client.get_limited_data_by_day("dividends", yesterday)
            
            if dividends is None or dividends.empty:
                logger.info(f"📅 No data for {yesterday}, trying {today}")
                dividends = self.jquants_client.get_limited_data_by_day("dividends", today)
            
            if dividends is None:
                return {"status": "failed", "error": "API call failed"}
            
            if dividends.empty:
                return {"status": "success", "records": 0, "message": "No dividend data for recent dates"}
            
            # Add metadata
            dividends['ingestion_timestamp'] = datetime.now()
            dividends['emergency_mode'] = True
            
            # Insert into BigQuery
            count = self.bigquery_client.insert_dataframe(
                table_name='dividends',
                dataframe=dividends,
                write_disposition='WRITE_APPEND'
            )
            
            logger.info(f"✅ Emergency dividend sample complete: {count} records")
            
            return {
                "status": "success",
                "records": count,
                "date_range": f"{yesterday} to {today}",
                "table": "dividends"
            }
            
        except Exception as e:
            logger.error(f"❌ Emergency dividend sample failed: {e}")
            return {"status": "failed", "error": str(e)}
    
    def minimal_data_check(self) -> Dict[str, Any]:
        """Check what data we can access with minimal API calls."""
        logger.info("🔍 Minimal data access check...")
        
        results = {}
        
        # Test connectivity first
        if not self.test_basic_connectivity():
            return {"status": "failed", "error": "Basic connectivity test failed"}
        
        results["connectivity"] = "success"
        
        # Try a single dividend request
        dividend_result = self.emergency_dividend_sample()
        results["dividend_sample"] = dividend_result
        
        # Get client stats
        results["client_stats"] = self.jquants_client.get_stats()
        
        return results
    
    def wait_and_retry_strategy(self) -> Dict[str, Any]:
        """Implement a wait-and-retry strategy for rate limited APIs."""
        logger.info("⏰ Starting wait-and-retry strategy...")
        
        # Wait progressively longer times and test connectivity
        wait_times = [300, 600, 1200, 1800, 3600]  # 5min, 10min, 20min, 30min, 1hr
        
        for i, wait_time in enumerate(wait_times):
            logger.info(f"⏱️  Attempt {i+1}/{len(wait_times)}: Waiting {wait_time}s ({wait_time//60} minutes)")
            
            # Show countdown
            for remaining in range(wait_time, 0, -60):
                logger.info(f"   ⏳ {remaining//60} minutes remaining...")
                time.sleep(60)
            
            logger.info("🔌 Testing connectivity after wait...")
            if self.test_basic_connectivity():
                logger.info("✅ Connectivity restored! Attempting data ingestion...")
                return self.minimal_data_check()
            else:
                logger.warning(f"❌ Still rate limited after {wait_time}s wait")
        
        logger.error("😞 All retry attempts exhausted")
        return {"status": "exhausted", "message": "All retry attempts failed"}


def main():
    """Main emergency ingestion function."""
    logger.info("🚨 EMERGENCY J-QUANTS INGESTION SERVICE")
    logger.info("=" * 60)
    logger.info("This service uses ultra-conservative rate limiting")
    logger.info("to work around the current 429 rate limit issues.")
    logger.info("=" * 60)
    
    try:
        service = EmergencyIngestionService()
        
        # First, try minimal data check
        logger.info("🔍 Step 1: Minimal data access test...")
        result = service.minimal_data_check()
        
        if result.get("status") == "failed":
            logger.warning("⚠️  Initial test failed, starting wait-and-retry strategy...")
            result = service.wait_and_retry_strategy()
        
        # Print final results
        logger.info("=" * 60)
        logger.info("📊 EMERGENCY INGESTION RESULTS")
        logger.info("=" * 60)
        
        for key, value in result.items():
            logger.info(f"{key}: {value}")
        
        logger.info("=" * 60)
        
        return 0 if result.get("status") == "success" else 1
        
    except Exception as e:
        logger.error(f"💥 Emergency ingestion service failed: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())