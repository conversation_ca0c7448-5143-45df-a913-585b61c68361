#!/usr/bin/env python3
"""Web interface for the J-Quants ingestion service (for Cloud Run)."""

import os
import json
import logging
from flask import Flask, request, jsonify
from production_ingestion_service import ProductionJQuantsService

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

@app.route('/', methods=['GET'])
def health_check():
    """Health check endpoint."""
    return jsonify({
        "status": "healthy",
        "service": "J-Quants Data Ingestion Service",
        "version": "1.0.0"
    })

@app.route('/status', methods=['GET'])
def get_status():
    """Get current ingestion status."""
    try:
        service = ProductionJQuantsService()
        status = service.get_status()
        return jsonify(status)
    except Exception as e:
        logger.error(f"Failed to get status: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/ingest', methods=['POST'])
def run_ingestion():
    """Run data ingestion based on mode parameter."""
    try:
        data = request.get_json() or {}
        mode = data.get('mode', 'daily-update')
        
        logger.info(f"Starting ingestion with mode: {mode}")
        
        service = ProductionJQuantsService()
        
        if mode == 'full-backfill':
            results = service.run_full_historical_backfill()
        elif mode == 'daily-update':
            results = service.daily_update()
        elif mode == 'companies-only':
            results = {"companies": service.ingest_all_companies()}
        elif mode == 'prices-only':
            results = {"daily_prices": service.ingest_all_historical_prices()}
        elif mode == 'financials-only':
            results = {"financial_statements": service.ingest_all_financial_statements()}
        else:
            return jsonify({"error": f"Unknown mode: {mode}"}), 400
        
        logger.info(f"Ingestion complete: {results}")
        
        return jsonify({
            "status": "success",
            "mode": mode,
            "results": results
        })
        
    except Exception as e:
        logger.error(f"Ingestion failed: {e}")
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500

@app.route('/trigger', methods=['POST'])
def trigger_ingestion():
    """Trigger ingestion (for Cloud Scheduler)."""
    return run_ingestion()

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 8080))
    app.run(host='0.0.0.0', port=port, debug=False)
