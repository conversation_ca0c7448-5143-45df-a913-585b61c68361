#!/usr/bin/env python3
"""
Flask web service for J-Quants data ingestion.
Handles Cloud Scheduler triggers and manual ingestion requests.
"""

import os
import json
import logging
from datetime import datetime
from flask import Flask, request, jsonify
from production_ingestion_service import ProductionJQuantsService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)

# Initialize the ingestion service
try:
    service = ProductionJQuantsService()
    logger.info("✅ Production J-Quants service initialized")
except Exception as e:
    logger.error(f"❌ Failed to initialize service: {e}")
    service = None


@app.route('/', methods=['GET'])
def health_check():
    """Health check endpoint."""
    return jsonify({
        'status': 'healthy',
        'service': 'J-Quants Data Ingestion',
        'timestamp': datetime.now().isoformat(),
        'service_initialized': service is not None
    })


@app.route('/trigger', methods=['POST'])
def trigger_ingestion():
    """
    Trigger data ingestion via Cloud Scheduler.
    Expected payload: {"mode": "daily-update"}
    """
    try:
        if not service:
            return jsonify({'error': 'Service not initialized'}), 500

        # Get request data
        data = request.get_json() or {}
        mode = data.get('mode', 'daily-update')

        logger.info(f"🚀 Triggered ingestion with mode: {mode}")

        # Execute ingestion based on mode
        if mode == 'daily-update':
            results = service.daily_update()
        elif mode == 'companies-only':
            results = {"companies": service.ingest_all_companies()}
        elif mode == 'prices-only':
            results = {"daily_prices": service.ingest_all_historical_prices()}
        elif mode == 'dividends-only':
            results = {"dividends": service.ingest_all_dividends()}
        else:
            return jsonify({'error': f'Unknown mode: {mode}'}), 400

        logger.info(f"✅ Ingestion completed: {results}")

        return jsonify({
            'status': 'success',
            'mode': mode,
            'results': results,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"❌ Ingestion failed: {e}")
        return jsonify({
            'status': 'error',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500


@app.route('/ingest', methods=['POST'])
def manual_ingest():
    """
    Manual ingestion endpoint with more options.
    Expected payload: {
        "mode": "daily-update|bulk-historical|companies-only|prices-only|dividends-only",
        "dry_run": false,
        "force": false
    }
    """
    try:
        if not service:
            return jsonify({'error': 'Service not initialized'}), 500

        # Get request data
        data = request.get_json() or {}
        mode = data.get('mode', 'daily-update')
        dry_run = data.get('dry_run', False)
        force = data.get('force', False)

        logger.info(f"🔧 Manual ingestion: mode={mode}, dry_run={dry_run}, force={force}")

        if dry_run:
            # Return status without executing
            status = service.get_status()
            return jsonify({
                'status': 'dry_run',
                'mode': mode,
                'current_status': status,
                'timestamp': datetime.now().isoformat()
            })

        # Execute ingestion
        if mode == 'daily-update':
            results = service.daily_update()
        elif mode == 'bulk-historical':
            results = service.bulk_historical_ingestion()
        elif mode == 'companies-only':
            results = {"companies": service.ingest_all_companies()}
        elif mode == 'prices-only':
            results = {"daily_prices": service.ingest_all_historical_prices()}
        elif mode == 'dividends-only':
            results = {"dividends": service.ingest_all_dividends()}
        elif mode == 'status':
            results = service.get_status()
        else:
            return jsonify({'error': f'Unknown mode: {mode}'}), 400

        logger.info(f"✅ Manual ingestion completed: {results}")

        return jsonify({
            'status': 'success',
            'mode': mode,
            'results': results,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"❌ Manual ingestion failed: {e}")
        return jsonify({
            'status': 'error',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500


@app.route('/status', methods=['GET'])
def get_status():
    """Get current ingestion status."""
    try:
        if not service:
            return jsonify({'error': 'Service not initialized'}), 500

        status = service.get_status()

        return jsonify({
            'status': 'success',
            'data': status,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"❌ Status check failed: {e}")
        return jsonify({
            'status': 'error',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500


if __name__ == '__main__':
    # Get port from environment (Cloud Run sets PORT)
    port = int(os.environ.get('PORT', 8080))

    logger.info(f"🚀 Starting J-Quants web service on port {port}")

    # Run the Flask app
    app.run(
        host='0.0.0.0',
        port=port,
        debug=False  # Never use debug=True in production
    )
