# Makefile for J-Quants to BigQuery Pipeline

.PHONY: help install test setup-gcp setup-bigquery run validate clean env-list env-switch env-create env-current gcp-accounts gcp-auth gcp-current

# Default target
help:
	@echo "J-Quants to BigQuery Pipeline"
	@echo "============================="
	@echo ""
	@echo "Available commands:"
	@echo "  install      - Install Python dependencies"
	@echo "  test         - Run setup tests"
	@echo "  setup-gcp    - Set up GCP project and services"
	@echo "  setup-bigquery - Set up BigQuery infrastructure only"
	@echo "  run          - Run the data pipeline"
	@echo "  run-date     - Run pipeline for specific date (DATE=YYYY-MM-DD)"
	@echo "  validate     - Run data quality validation"
	@echo "  clean        - Clean up temporary files"
	@echo ""
	@echo "Environment Management:"
	@echo "  env-list     - List all environments"
	@echo "  env-switch   - Switch environment (ENV=env_name)"
	@echo "  env-create   - Create new environment (ENV=env_name PROJECT=project_id ACCOUNT=gcp_account)"
	@echo "  env-current  - Show current environment info"
	@echo ""
	@echo "GCP Account Management:"
	@echo "  gcp-accounts - List authenticated GCP accounts"
	@echo "  gcp-auth     - Authenticate GCP account (ACCOUNT=email)"
	@echo "  gcp-current  - Show current GCP configuration"
	@echo ""
	@echo "Examples:"
	@echo "  make install"
	@echo "  make test"
	@echo "  make setup-gcp PROJECT=my-new-project ACCOUNT=<EMAIL>"
	@echo "  make env-create ENV=production PROJECT=my-prod-project ACCOUNT=<EMAIL>"
	@echo "  make env-switch ENV=production"
	@echo "  make gcp-auth ACCOUNT=<EMAIL>"
	@echo "  make run"
	@echo "  make run-date DATE=2024-01-15"
	@echo "  make validate"

# Install dependencies
install:
	@echo "Installing Python dependencies..."
	pip install -r requirements.txt
	@echo "Dependencies installed successfully!"

# Run setup tests
test:
	@echo "Running setup tests..."
	python test_setup.py

# Set up GCP project
setup-gcp:
	@echo "Setting up GCP project..."
	@if [ -n "$(PROJECT)" ] && [ -n "$(ACCOUNT)" ]; then \
		python setup_gcp.py --project-id $(PROJECT) --gcp-account $(ACCOUNT) --region asia-northeast1; \
	elif [ -n "$(PROJECT)" ]; then \
		python setup_gcp.py --project-id $(PROJECT) --gcp-account <EMAIL> --region asia-northeast1; \
	else \
		python setup_gcp.py --project-id tokyo_tickers --gcp-account <EMAIL> --region asia-northeast1; \
	fi

# Set up BigQuery infrastructure only
setup-bigquery:
	@echo "Setting up BigQuery infrastructure..."
	python main.py --setup-only

# Run the data pipeline
run:
	@echo "Running J-Quants data pipeline..."
	python main.py

# Run pipeline for specific date
run-date:
	@if [ -z "$(DATE)" ]; then \
		echo "Error: Please specify DATE. Example: make run-date DATE=2024-01-15"; \
		exit 1; \
	fi
	@echo "Running pipeline for date: $(DATE)"
	python main.py --date $(DATE)

# Run pipeline with replacement
run-replace:
	@if [ -z "$(DATE)" ]; then \
		echo "Error: Please specify DATE. Example: make run-replace DATE=2024-01-15"; \
		exit 1; \
	fi
	@echo "Running pipeline with replacement for date: $(DATE)"
	python main.py --date $(DATE) --replace

# Run data validation
validate:
	@echo "Running data quality validation..."
	python main.py --validate-only

# Get JSON output
run-json:
	@echo "Running pipeline with JSON output..."
	python main.py --output-json

# Clean up temporary files
clean:
	@echo "Cleaning up temporary files..."
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type f -name "*.log" -delete
	@echo "Cleanup completed!"

# Development helpers
dev-install:
	@echo "Installing development dependencies..."
	pip install -r requirements.txt
	pip install pytest black flake8 mypy
	@echo "Development dependencies installed!"

# Format code
format:
	@echo "Formatting code with black..."
	black *.py
	@echo "Code formatting completed!"

# Lint code
lint:
	@echo "Linting code with flake8..."
	flake8 *.py --max-line-length=100 --ignore=E203,W503
	@echo "Linting completed!"

# Type check
typecheck:
	@echo "Type checking with mypy..."
	mypy *.py --ignore-missing-imports
	@echo "Type checking completed!"

# Full development check
check: format lint typecheck test
	@echo "All checks completed!"

# Environment management commands
env-list:
	@echo "Listing all environments..."
	python env_manager.py list

env-switch:
	@if [ -z "$(ENV)" ]; then \
		echo "Error: Please specify ENV. Example: make env-switch ENV=production"; \
		exit 1; \
	fi
	@echo "Switching to environment: $(ENV)"
	python env_manager.py switch $(ENV)

env-create:
	@if [ -z "$(ENV)" ] || [ -z "$(PROJECT)" ] || [ -z "$(ACCOUNT)" ]; then \
		echo "Error: Please specify ENV, PROJECT, and ACCOUNT. Example: make env-create ENV=prod PROJECT=my-project ACCOUNT=<EMAIL>"; \
		exit 1; \
	fi
	@echo "Creating environment: $(ENV) for project: $(PROJECT) with account: $(ACCOUNT)"
	python env_manager.py create $(ENV) $(PROJECT) $(ACCOUNT) --region asia-northeast1

env-current:
	@echo "Current environment info:"
	python env_manager.py current

# GCP Account management commands
gcp-accounts:
	@echo "Listing authenticated GCP accounts..."
	python gcp_account_manager.py list

gcp-auth:
	@if [ -z "$(ACCOUNT)" ]; then \
		echo "Error: Please specify ACCOUNT. Example: make gcp-auth ACCOUNT=<EMAIL>"; \
		exit 1; \
	fi
	@echo "Authenticating GCP account: $(ACCOUNT)"
	python gcp_account_manager.py auth $(ACCOUNT)

gcp-current:
	@echo "Current GCP configuration:"
	python gcp_account_manager.py current
