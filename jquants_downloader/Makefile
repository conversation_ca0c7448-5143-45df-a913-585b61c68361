# J-Quants Downloader Makefile

.PHONY: install test lint format clean deploy setup

# Development
install:
	pip install -e ".[dev]"
	pre-commit install

test:
	pytest tests/ -v --cov=src --cov-report=html

test-unit:
	pytest tests/unit/ -v

test-integration:
	pytest tests/integration/ -v

lint:
	black --check src/ tests/
	flake8 src/ tests/
	mypy src/

format:
	black src/ tests/
	isort src/ tests/

clean:
	rm -rf build/ dist/ *.egg-info/
	rm -rf .pytest_cache/ .coverage htmlcov/
	find . -type d -name __pycache__ -delete
	find . -type f -name "*.pyc" -delete

# Setup
setup-gcp:
	python scripts/setup_gcp.py

setup-config:
	cp .env.example .env
	@echo "Please edit .env with your configuration"

# Deployment
deploy:
	./deployment/gcp/deploy.sh

docker-build:
	docker build -f deployment/docker/Dockerfile -t jquants-downloader .

docker-run:
	docker run --env-file .env jquants-downloader

# Data operations
run-comprehensive:
	python -m src.jquants_downloader.main --mode=comprehensive

run-daily:
	python -m src.jquants_downloader.main --mode=daily

validate-data:
	python -m src.jquants_downloader.services.validation_service

# Quick commands
auth:
	python -m src.jquants_downloader.utils.auth

status:
	python -c "from src.jquants_downloader.services.validation_service import main; main()"