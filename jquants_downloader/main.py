#!/usr/bin/env python3
"""Main entry point for J-Quants data downloader."""

import argparse
import json
import sys
from datetime import datetime
from typing import Optional

from data_pipeline import DataPipeline


def main():
    """Main function to run the J-Quants data pipeline."""
    parser = argparse.ArgumentParser(description="J-Quants to BigQuery Data Pipeline")
    
    parser.add_argument(
        "--setup-only",
        action="store_true",
        help="Only setup BigQuery infrastructure without ingesting data"
    )
    
    parser.add_argument(
        "--date",
        type=str,
        help="Specific date to fetch data for (YYYY-MM-DD format). If not provided, fetches current data."
    )
    
    parser.add_argument(
        "--replace",
        action="store_true",
        help="Replace existing data for the specified date"
    )
    
    parser.add_argument(
        "--validate-only",
        action="store_true",
        help="Only run data quality validation without ingesting new data"
    )
    
    parser.add_argument(
        "--output-json",
        action="store_true",
        help="Output results in JSON format"
    )
    
    args = parser.parse_args()
    
    try:
        # Initialize pipeline
        pipeline = DataPipeline()
        
        if args.setup_only:
            # Only setup BigQuery infrastructure
            print("Setting up BigQuery infrastructure...")
            pipeline.setup_bigquery()
            print("BigQuery setup completed successfully!")
            return 0
        
        elif args.validate_only:
            # Only run validation
            print("Running data quality validation...")
            validation_results = pipeline.validate_data_quality()
            
            if args.output_json:
                print(json.dumps(validation_results, indent=2))
            else:
                print(f"Validation Status: {'PASSED' if validation_results.get('validation_passed') else 'FAILED'}")
                print(f"Total Companies: {validation_results.get('total_companies', 0)}")
                if not validation_results.get('validation_passed'):
                    print(f"Issues: {validation_results.get('missing_fields', [])}")
            
            return 0 if validation_results.get('validation_passed') else 1
        
        else:
            # Run full pipeline
            print("Starting J-Quants data pipeline...")
            if args.date:
                print(f"Target date: {args.date}")
            if args.replace:
                print("Will replace existing data")
            
            results = pipeline.run_full_pipeline(
                target_date=args.date,
                replace_existing=args.replace
            )
            
            if args.output_json:
                print(json.dumps(results, indent=2))
            else:
                print(f"\nPipeline Status: {results['pipeline_status']}")
                print(f"Execution Time: {results['execution_time_seconds']:.2f} seconds")
                print(f"Companies Ingested: {results.get('companies_ingested', 0)}")
                
                validation = results.get('validation_results', {})
                print(f"Data Validation: {'PASSED' if validation.get('validation_passed') else 'FAILED'}")
                print(f"Total Companies in BigQuery: {validation.get('total_companies', 0)}")
                
                if validation.get('latest_records'):
                    print("\nSample of latest records:")
                    for i, record in enumerate(validation['latest_records'][:3], 1):
                        print(f"  {i}. {record.get('code')} - {record.get('company_name')} ({record.get('date')})")
            
            return 0 if results['pipeline_status'] == 'SUCCESS' else 1
    
    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
        return 1
    
    except Exception as e:
        print(f"Error: {e}")
        if args.output_json:
            error_result = {
                "pipeline_status": "ERROR",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
            print(json.dumps(error_result, indent=2))
        return 1


if __name__ == "__main__":
    sys.exit(main())
