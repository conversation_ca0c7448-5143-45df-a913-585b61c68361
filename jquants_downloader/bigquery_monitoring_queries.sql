-- BigQuery Monitoring Queries for J-Quants Data Ingestion
-- Replace {PROJECT_ID} and {DATASET_ID} with your actual values

-- ============================================
-- 1. TABLE SIZE AND ROW COUNT OVERVIEW
-- ============================================
SELECT 
    table_name,
    table_type,
    row_count,
    ROUND(size_bytes / POW(10, 9), 2) as size_gb,
    creation_time,
    last_modified_time
FROM `{PROJECT_ID}.{DATASET_ID}.INFORMATION_SCHEMA.TABLE_STORAGE`
ORDER BY size_bytes DESC;

-- ============================================
-- 2. DATA FRESHNESS CHECK
-- ============================================
WITH freshness AS (
    SELECT 'companies' as table_name, 
           MAX(date) as latest_date,
           MAX(ingestion_timestamp) as latest_ingestion,
           COUNT(*) as row_count,
           COUNT(DISTINCT date) as unique_dates
    FROM `{PROJECT_ID}.{DATASET_ID}.companies`
    
    UNION ALL
    
    SELECT 'daily_prices' as table_name,
           MAX(date) as latest_date,
           MAX(ingestion_timestamp) as latest_ingestion,
           COUNT(*) as row_count,
           COUNT(DISTINCT date) as unique_dates
    FROM `{PROJECT_ID}.{DATASET_ID}.daily_prices`
    
    UNION ALL
    
    SELECT 'financial_statements' as table_name,
           MAX(date) as latest_date,
           MAX(ingestion_timestamp) as latest_ingestion,
           COUNT(*) as row_count,
           COUNT(DISTINCT date) as unique_dates
    FROM `{PROJECT_ID}.{DATASET_ID}.financial_statements`
)
SELECT 
    table_name,
    row_count,
    unique_dates,
    latest_date,
    DATE_DIFF(CURRENT_DATE(), latest_date, DAY) as days_behind,
    latest_ingestion,
    TIMESTAMP_DIFF(CURRENT_TIMESTAMP(), latest_ingestion, HOUR) as hours_since_update
FROM freshness
ORDER BY table_name;

-- ============================================
-- 3. INGESTION RATE - LAST 24 HOURS
-- ============================================
SELECT 
    DATE(ingestion_timestamp) as ingestion_date,
    EXTRACT(HOUR FROM ingestion_timestamp) as hour,
    COUNT(*) as records_ingested,
    COUNT(DISTINCT code) as unique_stocks
FROM `{PROJECT_ID}.{DATASET_ID}.daily_prices`
WHERE ingestion_timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 24 HOUR)
GROUP BY ingestion_date, hour
ORDER BY ingestion_date DESC, hour DESC;

-- ============================================
-- 4. RECENT LOAD JOBS (LAST 24 HOURS)
-- ============================================
SELECT 
    creation_time,
    job_type,
    state,
    error_result.reason as error_reason,
    destination_table.table_id as table_name,
    total_bytes_processed,
    TIMESTAMP_DIFF(end_time, start_time, SECOND) as duration_seconds
FROM `{PROJECT_ID}.region-us`.INFORMATION_SCHEMA.JOBS_BY_PROJECT
WHERE creation_time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 24 HOUR)
    AND job_type = 'LOAD'
    AND destination_table.dataset_id = '{DATASET_ID}'
ORDER BY creation_time DESC
LIMIT 50;

-- ============================================
-- 5. DATA COVERAGE BY COMPANY
-- ============================================
WITH company_coverage AS (
    SELECT 
        c.code,
        c.company_name,
        c.market_code_name,
        COUNT(DISTINCT dp.date) as price_days,
        COUNT(DISTINCT fs.disclosure_date) as financial_reports
    FROM `{PROJECT_ID}.{DATASET_ID}.companies` c
    LEFT JOIN `{PROJECT_ID}.{DATASET_ID}.daily_prices` dp ON c.code = dp.code
    LEFT JOIN `{PROJECT_ID}.{DATASET_ID}.financial_statements` fs ON c.code = fs.code
    GROUP BY c.code, c.company_name, c.market_code_name
)
SELECT 
    market_code_name,
    COUNT(*) as company_count,
    AVG(price_days) as avg_price_days,
    AVG(financial_reports) as avg_financial_reports,
    SUM(CASE WHEN price_days = 0 THEN 1 ELSE 0 END) as companies_without_prices,
    SUM(CASE WHEN financial_reports = 0 THEN 1 ELSE 0 END) as companies_without_financials
FROM company_coverage
GROUP BY market_code_name
ORDER BY company_count DESC;

-- ============================================
-- 6. PARTITION INFORMATION
-- ============================================
SELECT 
    table_name,
    partition_id,
    total_rows,
    ROUND(total_logical_bytes / POW(10, 6), 2) as size_mb,
    last_modified_time
FROM `{PROJECT_ID}.{DATASET_ID}.INFORMATION_SCHEMA.PARTITIONS`
WHERE partition_id != '__NULL__'
ORDER BY table_name, partition_id DESC
LIMIT 20;

-- ============================================
-- 7. FAILED JOBS ANALYSIS
-- ============================================
SELECT 
    DATE(creation_time) as date,
    COUNT(*) as failed_jobs,
    STRING_AGG(DISTINCT error_result.reason, ', ') as error_reasons,
    STRING_AGG(DISTINCT destination_table.table_id, ', ') as affected_tables
FROM `{PROJECT_ID}.region-us`.INFORMATION_SCHEMA.JOBS_BY_PROJECT
WHERE creation_time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 7 DAY)
    AND state = 'FAILED'
    AND destination_table.dataset_id = '{DATASET_ID}'
GROUP BY date
ORDER BY date DESC;

-- ============================================
-- 8. DAILY INGESTION SUMMARY
-- ============================================
WITH daily_summary AS (
    SELECT 
        DATE(ingestion_timestamp) as ingestion_date,
        'companies' as table_name,
        COUNT(*) as records_added
    FROM `{PROJECT_ID}.{DATASET_ID}.companies`
    WHERE ingestion_timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 7 DAY)
    GROUP BY ingestion_date
    
    UNION ALL
    
    SELECT 
        DATE(ingestion_timestamp) as ingestion_date,
        'daily_prices' as table_name,
        COUNT(*) as records_added
    FROM `{PROJECT_ID}.{DATASET_ID}.daily_prices`
    WHERE ingestion_timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 7 DAY)
    GROUP BY ingestion_date
    
    UNION ALL
    
    SELECT 
        DATE(ingestion_timestamp) as ingestion_date,
        'financial_statements' as table_name,
        COUNT(*) as records_added
    FROM `{PROJECT_ID}.{DATASET_ID}.financial_statements`
    WHERE ingestion_timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 7 DAY)
    GROUP BY ingestion_date
)
SELECT 
    ingestion_date,
    STRING_AGG(CONCAT(table_name, ': ', CAST(records_added AS STRING)), ', ') as ingestion_summary,
    SUM(records_added) as total_records
FROM daily_summary
GROUP BY ingestion_date
ORDER BY ingestion_date DESC;

-- ============================================
-- 9. DATA QUALITY CHECKS
-- ============================================
-- Check for duplicate records
SELECT 
    'daily_prices' as table_name,
    COUNT(*) as total_records,
    COUNT(DISTINCT CONCAT(code, '|', CAST(date AS STRING))) as unique_records,
    COUNT(*) - COUNT(DISTINCT CONCAT(code, '|', CAST(date AS STRING))) as duplicate_records
FROM `{PROJECT_ID}.{DATASET_ID}.daily_prices`

UNION ALL

SELECT 
    'companies' as table_name,
    COUNT(*) as total_records,
    COUNT(DISTINCT CONCAT(code, '|', CAST(date AS STRING))) as unique_records,
    COUNT(*) - COUNT(DISTINCT CONCAT(code, '|', CAST(date AS STRING))) as duplicate_records
FROM `{PROJECT_ID}.{DATASET_ID}.companies`;

-- ============================================
-- 10. STORAGE COST ESTIMATION
-- ============================================
SELECT 
    SUM(size_bytes) / POW(10, 12) as total_size_tb,
    ROUND(SUM(size_bytes) / POW(10, 12) * 20, 2) as estimated_monthly_storage_cost_usd,
    COUNT(*) as total_tables
FROM `{PROJECT_ID}.{DATASET_ID}.INFORMATION_SCHEMA.TABLE_STORAGE`;