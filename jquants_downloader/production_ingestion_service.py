#!/usr/bin/env python3
"""Production J-Quants data ingestion service for GCP deployment."""

import logging
import time
import json
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Set
import pandas as pd
from jquantsapi import Client
from dotenv import load_dotenv
import os


from bigquery_client import BigQueryClient
from config import Config

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ProductionJQuantsService:
    """Production-grade J-Quants data ingestion service."""

    def __init__(self):
        """Initialize the production service."""
        load_dotenv()

        # Initialize clients
        self.jquants_client = Client(
            mail_address=Config.JQUANTS_EMAIL,
            password=Config.JQUANTS_PASSWORD
        )
        self.bigquery_client = BigQueryClient(
            project_id=Config.GOOGLE_CLOUD_PROJECT,
            dataset_id=Config.BIGQUERY_DATASET
        )

        # Rate limiting settings (VERY conservative to avoid 429 errors)
        self.request_delay = 60.0  # 10 seconds between requests
        self.batch_delay = 300.0    # 30 seconds between batches
        self.max_retries = 3
        # Error handling for rate limits
        self.error_backoff = 1800  # 30 minutes on 429
        self.max_consecutive_errors = 3
        self.consecutive_errors = 0

        # Simple state tracking (in-memory for now)
        self.state = {
            "companies_last_updated": None,
            "prices_last_date": None,
            "financials_last_updated": None,
            "processed_companies": [],
            "failed_companies": [],
            "total_records_ingested": {
                "companies": 0,
                "daily_prices": 0,
                "financial_statements": 0
            }
        }

        logger.info("Production J-Quants service initialized")

    def _save_state(self):
        """Save ingestion state (simplified - just log for now)."""
        logger.info(f"State update: {self.state['total_records_ingested']}")

    def _get_processed_companies_for_prices(self) -> Set[str]:
        """Get set of companies that have been processed for historical prices."""
        try:
            # Query BigQuery to see which companies we already have price data for
            query = """
            SELECT DISTINCT code
            FROM `{}.{}.daily_prices`
            WHERE date <= DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY)
            """.format(Config.GOOGLE_CLOUD_PROJECT, Config.BIGQUERY_DATASET)

            result = self.bigquery_client.client.query(query).result()
            return {row.code for row in result}
        except Exception as e:
            logger.warning(f"Could not get processed companies: {e}")
            return set()

    
    def _handle_rate_limit_error(self, e: Exception, context: str = ""):
        """Handle 429 rate limit errors with exponential backoff."""
        error_msg = str(e).lower()
        
        if '429' in error_msg or 'rate limit' in error_msg or 'too many' in error_msg:
            self.consecutive_errors += 1
            
            if self.consecutive_errors >= self.max_consecutive_errors:
                logger.error(f"❌ Too many consecutive rate limit errors ({self.consecutive_errors}), stopping {context}")
                raise Exception(f"Rate limit exceeded after {self.consecutive_errors} attempts")
            
            # Exponential backoff
            wait_time = self.error_backoff * (2 ** (self.consecutive_errors - 1))
            logger.warning(f"🚨 Rate limited! Attempt {self.consecutive_errors}/{self.max_consecutive_errors}")
            logger.warning(f"⏱️  Waiting {wait_time} seconds before retry... ({context})")
            
            import time
            time.sleep(wait_time)
            return True  # Indicates this was a rate limit error
        
        return False  # Not a rate limit error
    
    def _reset_error_count(self):
        """Reset consecutive error count on success."""
        if self.consecutive_errors > 0:
            logger.info(f"✅ Success! Resetting error count (was {self.consecutive_errors})")
            self.consecutive_errors = 0

    def ingest_all_companies(self) -> int:
        """Ingest all companies data (always refresh)."""
        logger.info("🏢 Starting complete companies data ingestion...")

        try:
            companies_df = self.jquants_client.get_listed_info()
            logger.info(f"Retrieved {len(companies_df)} companies")

            # Transform data
            companies_df = companies_df.rename(columns={
                'Date': 'date',
                'Code': 'code',
                'CompanyName': 'company_name',
                'CompanyNameEnglish': 'company_name_english',
                'Sector17Code': 'sector17_code',
                'Sector17CodeName': 'sector17_code_name',
                'Sector33Code': 'sector33_code',
                'Sector33CodeName': 'sector33_code_name',
                'ScaleCategory': 'scale_category',
                'MarketCode': 'market_code',
                'MarketCodeName': 'market_code_name',
                'MarginCode': 'margin_code',
                'MarginCodeName': 'margin_code_name',
            })

            # Insert with WRITE_TRUNCATE to replace all data
            job_id = self.bigquery_client.insert_dataframe(
                table_name='companies',
                dataframe=companies_df,
                write_disposition='WRITE_TRUNCATE'
            )

            # Update state
            self.state["companies_last_updated"] = datetime.now().isoformat()
            self.state["total_records_ingested"]["companies"] = len(companies_df)
            self._save_state()

            logger.info(f"✅ Companies data ingested successfully. Job ID: {job_id}")
            return len(companies_df)

        except Exception as e:
            logger.error(f"❌ Companies ingestion failed: {e}")
            raise

    def ingest_historical_prices_for_company(self, code: str) -> int:
        """Ingest all historical price data for a single company."""
        try:
            logger.info(f"Fetching historical prices for {code}")

            # Get all available historical data for this company
            prices_df = self.jquants_client.get_prices_daily_quotes(code=code)

            if prices_df.empty:
                logger.info(f"No price data for {code}")
                return 0

            # Transform data
            prices_df = prices_df.rename(columns={
                'Date': 'date',
                'Code': 'code',
                'Open': 'open',
                'High': 'high',
                'Low': 'low',
                'Close': 'close',
                'Volume': 'volume',
                'TurnoverValue': 'turnover_value',
                'AdjustmentFactor': 'adjustment_factor',
                'AdjustmentOpen': 'adjustment_open',
                'AdjustmentHigh': 'adjustment_high',
                'AdjustmentLow': 'adjustment_low',
                'AdjustmentClose': 'adjustment_close',
                'AdjustmentVolume': 'adjustment_volume',
            })

            # Insert data
            job_id = self.bigquery_client.insert_dataframe(
                table_name='daily_prices',
                dataframe=prices_df,
                write_disposition='WRITE_APPEND'
            )

            logger.info(f"✅ Ingested {len(prices_df)} price records for {code}")
            return len(prices_df)

        except Exception as e:
            logger.error(f"❌ Failed to ingest prices for {code}: {e}")
            return 0

    def ingest_all_historical_prices(self) -> int:
        """Ingest historical price data for all companies."""
        logger.info("📈 Starting comprehensive historical prices ingestion...")

        # Get all companies
        companies_df = self.jquants_client.get_listed_info()
        all_codes = companies_df['Code'].tolist()

        # Get companies we've already processed
        processed_codes = self._get_processed_companies_for_prices()

        # Filter to only unprocessed companies
        remaining_codes = [code for code in all_codes if code not in processed_codes]

        logger.info(f"Total companies: {len(all_codes)}")
        logger.info(f"Already processed: {len(processed_codes)}")
        logger.info(f"Remaining to process: {len(remaining_codes)}")

        total_records = 0
        failed_companies = []

        for i, code in enumerate(remaining_codes):
            try:
                logger.info(f"Processing {code} ({i+1}/{len(remaining_codes)})")

                records = self.ingest_historical_prices_for_company(code)
                total_records += records

                # Update state periodically
                if (i + 1) % 100 == 0:
                    self.state["total_records_ingested"]["daily_prices"] += total_records
                    self.state["prices_last_date"] = datetime.now().isoformat()
                    self._save_state()
                    logger.info(f"Checkpoint: Processed {i+1} companies, {total_records:,} total records")
                    total_records = 0  # Reset counter

                # Rate limiting
                time.sleep(self.request_delay)

                # Longer break every 50 companies
                if (i + 1) % 50 == 0:
                    logger.info(f"Taking longer break after {i+1} companies...")
                    time.sleep(self.batch_delay * 2)

            except Exception as e:
                logger.error(f"Failed to process {code}: {e}")
                failed_companies.append(code)
                continue

        # Final state update
        self.state["total_records_ingested"]["daily_prices"] += total_records
        self.state["failed_companies"] = failed_companies
        self.state["prices_last_date"] = datetime.now().isoformat()
        self._save_state()

        logger.info(f"✅ Historical prices ingestion complete")
        logger.info(f"Total records ingested: {total_records:,}")
        logger.info(f"Failed companies: {len(failed_companies)}")

        return total_records

    def ingest_financial_statements_for_company(self, code: str) -> int:
        """Ingest financial statements for a single company."""
        try:
            logger.info(f"Fetching financial statements for {code}")

            statements_df = self.jquants_client.get_fins_statements(code=code)

            if statements_df.empty:
                return 0

            # Clean and transform data
            statements_df['date'] = statements_df['DisclosedDate']

            # Convert numeric columns
            numeric_columns = [
                'NetSales', 'OperatingProfit', 'OrdinaryProfit', 'Profit',
                'TotalAssets', 'Equity', 'CashFlowsFromOperatingActivities',
                'CashFlowsFromInvestingActivities', 'CashFlowsFromFinancingActivities',
                'CashAndEquivalents'
            ]

            for col in numeric_columns:
                if col in statements_df.columns:
                    statements_df[col] = pd.to_numeric(statements_df[col], errors='coerce')

            # Clean column names and rename
            def clean_column_name(name):
                import re
                cleaned = re.sub(r'[^a-zA-Z0-9_]', '_', name)
                cleaned = re.sub(r'_+', '_', cleaned)
                cleaned = cleaned.strip('_')
                if cleaned and not cleaned[0].isalpha() and cleaned[0] != '_':
                    cleaned = '_' + cleaned
                return cleaned.lower()

            statements_df.columns = [clean_column_name(col) for col in statements_df.columns]

            # Rename to standard names
            column_mapping = {
                'discloseddate': 'disclosure_date',
                'localcode': 'code',
                'disclosurenumber': 'disclosure_number',
                'typeofdocument': 'type_of_document',
                'typeofcurrentperiod': 'type_of_current_period',
                'netsales': 'net_sales',
                'operatingprofit': 'operating_profit',
                'ordinaryprofit': 'ordinary_profit',
                'profit': 'profit',
                'totalassets': 'total_assets',
                'equity': 'equity',
            }

            for old_col, new_col in column_mapping.items():
                if old_col in statements_df.columns:
                    statements_df = statements_df.rename(columns={old_col: new_col})

            # Select only columns we want
            schema_columns = [
                'date', 'disclosure_date', 'code', 'disclosure_number', 'type_of_document',
                'type_of_current_period', 'net_sales', 'operating_profit', 'ordinary_profit',
                'profit', 'total_assets', 'equity'
            ]

            available_columns = [col for col in schema_columns if col in statements_df.columns]
            statements_df = statements_df[available_columns]

            # Insert data
            job_id = self.bigquery_client.insert_dataframe(
                table_name='financial_statements',
                dataframe=statements_df,
                write_disposition='WRITE_APPEND'
            )

            logger.info(f"✅ Ingested {len(statements_df)} financial records for {code}")
            return len(statements_df)

        except Exception as e:
            logger.error(f"❌ Failed to ingest financials for {code}: {e}")
            return 0

    def ingest_all_financial_statements(self) -> int:
        """Ingest financial statements for all companies."""
        logger.info("💰 Starting comprehensive financial statements ingestion...")

        # Get all companies
        companies_df = self.jquants_client.get_listed_info()
        all_codes = companies_df['Code'].tolist()

        logger.info(f"Processing financial statements for {len(all_codes)} companies")

        total_records = 0
        failed_companies = []

        for i, code in enumerate(all_codes):
            try:
                logger.info(f"Processing financials for {code} ({i+1}/{len(all_codes)})")

                records = self.ingest_financial_statements_for_company(code)
                total_records += records

                # Update state periodically
                if (i + 1) % 50 == 0:
                    self.state["total_records_ingested"]["financial_statements"] += total_records
                    self.state["financials_last_updated"] = datetime.now().isoformat()
                    self._save_state()
                    logger.info(f"Checkpoint: Processed {i+1} companies, {total_records:,} total records")
                    total_records = 0

                # Rate limiting - be more conservative for financials
                time.sleep(self.request_delay * 2)

                # Longer break every 25 companies
                if (i + 1) % 25 == 0:
                    logger.info(f"Taking longer break after {i+1} companies...")
                    time.sleep(self.batch_delay * 3)

            except Exception as e:
                logger.error(f"Failed to process financials for {code}: {e}")
                failed_companies.append(code)
                continue

        # Final state update
        self.state["total_records_ingested"]["financial_statements"] += total_records
        self.state["failed_companies"].extend(failed_companies)
        self.state["financials_last_updated"] = datetime.now().isoformat()
        self._save_state()

        logger.info(f"✅ Financial statements ingestion complete")
        logger.info(f"Total records ingested: {total_records:,}")
        logger.info(f"Failed companies: {len(failed_companies)}")

        return total_records

    def ingest_all_dividends(self) -> int:
        """Ingest dividend data incrementally - Premium: Recent data first, then historical"""
        logger.info("💰 Starting INCREMENTAL dividend data ingestion (Premium)")

        total_records = 0

        try:
            # Start with recent data (last 2 years) - most likely to succeed
            current_year = datetime.now().year
            years_to_process = [current_year, current_year - 1, current_year - 2]

            for year in years_to_process:
                try:
                    start_date = datetime(year, 1, 1)
                    end_date = datetime(year, 12, 31)

                    # Don't go beyond current date
                    if end_date > datetime.now():
                        end_date = datetime.now()

                    logger.info(f"📅 Fetching dividend data for {year}: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")

                    dividends = self.jquants_client.get_dividend_range(
                        start_dt=start_date.strftime('%Y-%m-%d'),
                        end_dt=end_date.strftime('%Y-%m-%d')
                    )

                    # Handle case where API returns string instead of DataFrame
                    if isinstance(dividends, str) or dividends is None:
                        logger.warning(f"No dividend data found for {year} (API returned string/None)")
                        continue

                    if dividends.empty:
                        logger.warning(f"No dividend data found for {year}")
                        continue

                    # Clean dividend data - handle datetime columns properly
                    datetime_columns = ['AnnouncementDate', 'RecordDate', 'ExDate', 'ActualRecordDate', 'PayableDate', 'BoardMeetingDate']
                    
                    for col in dividends.columns:
                        if col == 'ingestion_timestamp':
                            continue  # Skip timestamp column
                        elif col in datetime_columns:
                            # Convert datetime columns to datetime64[ns] for BigQuery
                            dividends[col] = pd.to_datetime(dividends[col], errors='coerce')
                        else:
                            # Convert other columns to strings
                            dividends[col] = dividends[col].astype(str)
                    
                    # Add ingestion timestamp
                    dividends['ingestion_timestamp'] = datetime.now()

                    # Insert into BigQuery with WRITE_APPEND
                    job_id = self.bigquery_client.insert_dataframe(
                        table_name='dividends',
                        dataframe=dividends,
                        write_disposition='WRITE_APPEND'
                    )
                    
                    # Count actual records inserted
                    count = len(dividends)
                    total_records += count
                    logger.info(f"✅ Ingested {count} dividend records for {year}")
                    
                    # Reset error count on success
                    self._reset_error_count()

                    # Rate limiting between years
                    time.sleep(self.batch_delay)

                except Exception as e:
                    # Handle rate limit errors with backoff
                    if self._handle_rate_limit_error(e, f"dividends {year}"):
                        continue  # Retry this year
                    
                    logger.error(f"❌ Failed to ingest dividends for {year}: {e}")
                    continue

            logger.info(f"✅ Incremental dividend ingestion complete: {total_records:,} total records")
            return total_records

        except Exception as e:
            logger.error(f"❌ Failed to ingest dividends: {e}")
            # Don't raise - return partial results
            return total_records

    def ingest_all_margin_trading(self) -> int:
        """Ingest all margin trading data - Premium: ALL periods since Feb 10, 2012"""
        logger.info("📈 Starting margin trading data ingestion (Premium: ALL historical data)")

        try:
            # Premium subscription: ALL periods since February 10, 2012
            start_date = datetime(2012, 2, 10)
            end_date = datetime.now()

            logger.info(f"Fetching margin trading data from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
            logger.info("📊 Premium subscription: Getting ALL historical margin trading data")

            margin_data = self.jquants_client.get_weekly_margin_range(
                start_dt=start_date.strftime('%Y-%m-%d'),
                end_dt=end_date.strftime('%Y-%m-%d')
            )

            # Handle case where API returns string instead of DataFrame
            if isinstance(margin_data, str) or margin_data is None:
                logger.warning("No margin trading data found (API returned string/None)")
                return 0

            if margin_data.empty:
                logger.warning("No margin trading data found")
                return 0

            # Add ingestion timestamp
            margin_data['ingestion_timestamp'] = datetime.now()

            # Insert into BigQuery
            job_id = self.bigquery_client.insert_dataframe(
                table_name='margin_trading',
                dataframe=margin_data
            )
            
            # Count actual records inserted
            count = len(margin_data)

            logger.info(f"✅ Ingested {count} margin trading records (2012-2025)")
            return count

        except Exception as e:
            logger.error(f"❌ Failed to ingest margin trading data: {e}")
            raise

    def ingest_all_topix_data(self) -> int:
        """Ingest all TOPIX index data - Premium: ALL periods since May 7, 2008"""
        logger.info("📊 Starting TOPIX data ingestion (Premium: ALL historical data)")

        try:
            # Premium subscription: ALL periods since May 7, 2008
            start_date = datetime(2008, 5, 7)
            end_date = datetime.now()

            logger.info(f"Fetching TOPIX data from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
            logger.info("📊 Premium subscription: Getting ALL historical TOPIX data")

            # Use get_indices with TOPIX code (must be 4 characters)
            topix_data = self.jquants_client.get_indices(
                code="0101",  # TOPIX code (4 characters required)
                from_yyyymmdd=start_date.strftime('%Y-%m-%d'),
                to_yyyymmdd=end_date.strftime('%Y-%m-%d')
            )

            # Handle case where API returns string instead of DataFrame
            if isinstance(topix_data, str) or topix_data is None:
                logger.warning("No TOPIX data found (API returned string/None)")
                return 0

            if topix_data.empty:
                logger.warning("No TOPIX data found")
                return 0

            # Add ingestion timestamp
            topix_data['ingestion_timestamp'] = datetime.now()

            # Insert into BigQuery
            job_id = self.bigquery_client.insert_dataframe(
                table_name='topix_data',
                dataframe=topix_data
            )
            
            # Count actual records inserted
            count = len(topix_data)

            logger.info(f"✅ Ingested {count} TOPIX records (2008-2025)")
            return count

        except Exception as e:
            logger.error(f"❌ Failed to ingest TOPIX data: {e}")
            raise

    def ingest_all_indices_data(self) -> int:
        """Ingest all other indices data - Premium: ALL periods since May 7, 2008"""
        logger.info("📈 Starting indices data ingestion (Premium: ALL historical data)")

        try:
            # Premium subscription: ALL periods since May 7, 2008
            start_date = datetime(2008, 5, 7)
            end_date = datetime.now()

            logger.info(f"Fetching indices data from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
            logger.info("📊 Premium subscription: Getting ALL historical indices data")

            # Get multiple indices - need to specify codes
            # Common index codes: 0101=TOPIX, 0201=Nikkei 225, etc.
            all_indices = []
            index_codes = ["0201", "0301", "0401"]  # Nikkei 225, JPX-Nikkei 400, etc.
            
            for code in index_codes:
                try:
                    logger.info(f"Fetching index {code}...")
                    index_data = self.jquants_client.get_indices(
                        code=code,
                        from_yyyymmdd=start_date.strftime('%Y-%m-%d'),
                        to_yyyymmdd=end_date.strftime('%Y-%m-%d')
                    )
                    if isinstance(index_data, pd.DataFrame) and not index_data.empty:
                        all_indices.append(index_data)
                    time.sleep(2)  # Small delay between requests
                except Exception as e:
                    logger.warning(f"Failed to fetch index {code}: {e}")
                    continue
            
            # Combine all indices data
            if all_indices:
                indices_data = pd.concat(all_indices, ignore_index=True)
            else:
                indices_data = pd.DataFrame()

            # Handle case where API returns string instead of DataFrame
            if isinstance(indices_data, str) or indices_data is None:
                logger.warning("No indices data found (API returned string/None)")
                return 0

            if indices_data.empty:
                logger.warning("No indices data found")
                return 0

            # Add ingestion timestamp
            indices_data['ingestion_timestamp'] = datetime.now()

            # Insert into BigQuery
            job_id = self.bigquery_client.insert_dataframe(
                table_name='indices_data',
                dataframe=indices_data
            )
            
            # Count actual records inserted
            count = len(indices_data)

            logger.info(f"✅ Ingested {count} indices records (2008-2025)")
            return count

        except Exception as e:
            logger.error(f"❌ Failed to ingest indices data: {e}")
            raise

    def ingest_all_trading_by_type(self) -> int:
        """Ingest trading by type of investors - Premium: ALL periods since Jan 16, 2008"""
        logger.info("👥 Starting trading by type data ingestion (Premium: ALL historical data)")

        try:
            # Premium subscription: ALL periods since January 16, 2008
            start_date = datetime(2008, 1, 16)
            end_date = datetime.now()

            logger.info(f"Fetching trading by type data from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
            logger.info("📊 Premium subscription: Getting ALL historical trading by type data")

            trading_data = self.jquants_client.get_markets_trades_spec(
                from_yyyymmdd=start_date.strftime('%Y-%m-%d'),
                to_yyyymmdd=end_date.strftime('%Y-%m-%d')
            )

            if trading_data.empty:
                logger.warning("No trading by type data found")
                return 0

            # Add ingestion timestamp
            trading_data['ingestion_timestamp'] = datetime.now()

            # Insert into BigQuery
            job_id = self.bigquery_client.insert_dataframe(
                table_name='trading_by_type',
                dataframe=trading_data
            )
            
            # Count actual records inserted
            count = len(trading_data)

            logger.info(f"✅ Ingested {count} trading by type records (2008-2025)")
            return count

        except Exception as e:
            logger.error(f"❌ Failed to ingest trading by type data: {e}")
            raise

    def ingest_all_short_selling(self) -> int:
        """Ingest short selling by sector - Premium: ALL periods since Nov 5, 2008"""
        logger.info("📉 Starting short selling data ingestion (Premium: ALL historical data)")

        try:
            # Premium subscription: ALL periods since November 5, 2008
            start_date = datetime(2008, 11, 5)
            end_date = datetime.now()

            logger.info(f"Fetching short selling data from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
            logger.info("📊 Premium subscription: Getting ALL historical short selling data")

            short_data = self.jquants_client.get_markets_short_selling(
                from_yyyymmdd=start_date.strftime('%Y-%m-%d'),
                to_yyyymmdd=end_date.strftime('%Y-%m-%d')
            )

            if short_data.empty:
                logger.warning("No short selling data found")
                return 0

            # Add ingestion timestamp
            short_data['ingestion_timestamp'] = datetime.now()

            # Insert into BigQuery
            job_id = self.bigquery_client.insert_dataframe(
                table_name='short_selling',
                dataframe=short_data
            )
            
            # Count actual records inserted
            count = len(short_data)

            logger.info(f"✅ Ingested {count} short selling records (2008-2025)")
            return count

        except Exception as e:
            logger.error(f"❌ Failed to ingest short selling data: {e}")
            raise

    def ingest_all_breakdown_trading(self) -> int:
        """Ingest breakdown trading data - Premium: ALL periods since Apr 1, 2015"""
        logger.info("🔍 Starting breakdown trading data ingestion (Premium: ALL historical data)")

        try:
            # Premium subscription: ALL periods since April 1, 2015
            start_date = datetime(2015, 4, 1)
            end_date = datetime.now()

            logger.info(f"Fetching breakdown trading data from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
            logger.info("📊 Premium subscription: Getting ALL historical breakdown trading data")

            breakdown_data = self.jquants_client.get_markets_breakdown(
                from_yyyymmdd=start_date.strftime('%Y-%m-%d'),
                to_yyyymmdd=end_date.strftime('%Y-%m-%d')
            )

            if breakdown_data.empty:
                logger.warning("No breakdown trading data found")
                return 0

            # Add ingestion timestamp
            breakdown_data['ingestion_timestamp'] = datetime.now()

            # Insert into BigQuery
            job_id = self.bigquery_client.insert_dataframe(
                table_name='breakdown_trading',
                dataframe=breakdown_data
            )
            
            # Count actual records inserted
            count = len(breakdown_data)

            logger.info(f"✅ Ingested {count} breakdown trading records (2015-2025)")
            return count

        except Exception as e:
            logger.error(f"❌ Failed to ingest breakdown trading data: {e}")
            raise

    def ingest_all_financial_statements_detailed(self) -> int:
        """Ingest detailed financial statements (BS/PL) - Premium: ALL periods since Jan 13, 2009"""
        logger.info("💼 Starting detailed financial statements ingestion (Premium: ALL historical data)")

        try:
            # Premium subscription: ALL periods since January 13, 2009
            start_date = datetime(2009, 1, 13)
            end_date = datetime.now()

            logger.info(f"Fetching detailed financial statements from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
            logger.info("📊 Premium subscription: Getting ALL historical detailed financial statements")

            fin_data = self.jquants_client.get_fins_statements(
                from_yyyymmdd=start_date.strftime('%Y-%m-%d'),
                to_yyyymmdd=end_date.strftime('%Y-%m-%d')
            )

            if fin_data.empty:
                logger.warning("No detailed financial statements found")
                return 0

            # Add ingestion timestamp
            fin_data['ingestion_timestamp'] = datetime.now()

            # Insert into BigQuery
            job_id = self.bigquery_client.insert_dataframe(
                table_name='financial_statements_detailed',
                dataframe=fin_data
            )
            
            # Count actual records inserted
            count = len(fin_data)

            logger.info(f"✅ Ingested {count} detailed financial statement records (2009-2025)")
            return count

        except Exception as e:
            logger.error(f"❌ Failed to ingest detailed financial statements: {e}")
            raise

    def ingest_all_futures(self) -> int:
        """Ingest futures data - Premium: ALL periods since May 7, 2008"""
        logger.info("📈 Starting futures data ingestion (Premium: ALL historical data)")

        try:
            # Premium subscription: ALL periods since May 7, 2008
            start_date = datetime(2008, 5, 7)
            end_date = datetime.now()

            logger.info(f"Fetching futures data from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
            logger.info("📊 Premium subscription: Getting ALL historical futures data")

            futures_data = self.jquants_client.get_derivatives_futures(
                from_yyyymmdd=start_date.strftime('%Y-%m-%d'),
                to_yyyymmdd=end_date.strftime('%Y-%m-%d')
            )

            if futures_data.empty:
                logger.warning("No futures data found")
                return 0

            # Add ingestion timestamp
            futures_data['ingestion_timestamp'] = datetime.now()

            # Insert into BigQuery
            job_id = self.bigquery_client.insert_dataframe(
                table_name='futures',
                dataframe=futures_data
            )
            
            # Count actual records inserted
            count = len(futures_data)

            logger.info(f"✅ Ingested {count} futures records (2008-2025)")
            return count

        except Exception as e:
            logger.error(f"❌ Failed to ingest futures data: {e}")
            raise

    def ingest_all_options(self) -> int:
        """Ingest options data - Premium: ALL periods since May 7, 2008"""
        logger.info("📊 Starting options data ingestion (Premium: ALL historical data)")

        try:
            # Premium subscription: ALL periods since May 7, 2008
            start_date = datetime(2008, 5, 7)
            end_date = datetime.now()

            logger.info(f"Fetching options data from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
            logger.info("📊 Premium subscription: Getting ALL historical options data")

            options_data = self.jquants_client.get_derivatives_options(
                from_yyyymmdd=start_date.strftime('%Y-%m-%d'),
                to_yyyymmdd=end_date.strftime('%Y-%m-%d')
            )

            if options_data.empty:
                logger.warning("No options data found")
                return 0

            # Add ingestion timestamp
            options_data['ingestion_timestamp'] = datetime.now()

            # Insert into BigQuery
            job_id = self.bigquery_client.insert_dataframe(
                table_name='options',
                dataframe=options_data
            )
            
            # Count actual records inserted
            count = len(options_data)

            logger.info(f"✅ Ingested {count} options records (2008-2025)")
            return count

        except Exception as e:
            logger.error(f"❌ Failed to ingest options data: {e}")
            raise

    def ingest_all_index_options(self) -> int:
        """Ingest index options data - Premium: ALL periods since May 7, 2008"""
        logger.info("📈 Starting index options data ingestion (Premium: ALL historical data)")

        try:
            # Premium subscription: ALL periods since May 7, 2008
            start_date = datetime(2008, 5, 7)
            end_date = datetime.now()

            # Index options require a specific date (not date range)
            # Get yesterday's data as today's may not be available yet
            yesterday = (datetime.now() - timedelta(days=1))
            logger.info(f"Fetching index options data for {yesterday.strftime('%Y-%m-%d')}")
            logger.info("📊 Premium subscription: Getting latest index options data")

            index_options_data = self.jquants_client.get_option_index_option(
                date_yyyymmdd=yesterday.strftime('%Y-%m-%d')
            )

            if index_options_data.empty:
                logger.warning("No index options data found")
                return 0

            # Add ingestion timestamp
            index_options_data['ingestion_timestamp'] = datetime.now()

            # Insert into BigQuery
            job_id = self.bigquery_client.insert_dataframe(
                table_name='index_options',
                dataframe=index_options_data
            )
            
            # Count actual records inserted
            count = len(index_options_data)

            logger.info(f"✅ Ingested {count} index options records (2008-2025)")
            return count

        except Exception as e:
            logger.error(f"❌ Failed to ingest index options data: {e}")
            raise

    def bulk_historical_ingestion(self) -> Dict[str, int]:
        """
        ONE-TIME BULK HISTORICAL INGESTION with state persistence.
        Downloads ALL available data from ALL endpoints sequentially.
        Can resume from where it left off if interrupted.
        """
        logger.info("🚀 Starting BULK HISTORICAL INGESTION")
        logger.info("📊 This will download ALL available data from ALL J-Quants endpoints")
        logger.info("⏱️  Estimated time: 6-12 hours with aggressive rate limiting")
        logger.info("🔄 Can resume from last checkpoint if interrupted")

        # Define all data types to ingest in order (skip companies/prices if already done)
        data_types = [
            ("dividends", "💰 Dividend data", self.ingest_all_dividends),
            ("margin_trading", "📈 Margin trading data", self.ingest_all_margin_trading),
            ("topix_data", "📊 TOPIX data", self.ingest_all_topix_data),
            ("indices_data", "📈 Indices data", self.ingest_all_indices_data),
            ("trading_by_type", "👥 Trading by type data", self.ingest_all_trading_by_type),
            ("short_selling", "📉 Short selling data", self.ingest_all_short_selling),
            ("breakdown_trading", "🔍 Breakdown trading data", self.ingest_all_breakdown_trading),
            ("financial_statements_detailed", "💼 Detailed financial statements", self.ingest_all_financial_statements_detailed),
            ("futures", "📈 Futures data", self.ingest_all_futures),
            ("options", "📊 Options data", self.ingest_all_options),
            ("index_options", "📈 Index options data", self.ingest_all_index_options),
        ]

        results = {}
        start_time = datetime.now()

        # Check if we should resume from a previous run
        last_completed = self.state.get("bulk_ingestion_last_completed", None)
        start_index = 0

        if last_completed:
            try:
                start_index = next(i for i, (key, _, _) in enumerate(data_types) if key == last_completed) + 1
                logger.info(f"🔄 Resuming from checkpoint: {last_completed} (index {start_index})")
            except StopIteration:
                logger.info("🆕 Starting fresh bulk ingestion")

        # Process each data type sequentially
        for i, (data_type_key, description, ingest_function) in enumerate(data_types[start_index:], start_index):
            try:
                logger.info("=" * 80)
                logger.info(f"📊 STEP {i+1}/{len(data_types)}: {description}")
                logger.info("=" * 80)

                # Record start time for this data type
                step_start = datetime.now()

                # Ingest the data
                count = ingest_function()
                results[data_type_key] = count

                # Calculate step duration
                step_duration = datetime.now() - step_start
                logger.info(f"✅ {description} complete: {count:,} records in {step_duration}")

                # Update checkpoint
                self.state["bulk_ingestion_last_completed"] = data_type_key
                self.state["bulk_ingestion_progress"] = f"{i+1}/{len(data_types)}"
                self._save_state()

                # Aggressive rate limiting between data types
                logger.info(f"⏱️  Waiting {self.batch_delay} seconds before next data type...")
                time.sleep(self.batch_delay)

            except Exception as e:
                logger.error(f"❌ Failed to ingest {description}: {e}")
                results[data_type_key] = f"ERROR: {str(e)}"

                # Save error state but continue with next data type
                self.state[f"bulk_ingestion_error_{data_type_key}"] = str(e)
                self._save_state()

                # Wait longer after an error
                logger.info(f"⏱️  Error occurred, waiting {self.batch_delay * 2} seconds before continuing...")
                time.sleep(self.batch_delay * 2)
                continue

        # Mark bulk ingestion as complete
        total_duration = datetime.now() - start_time
        self.state["bulk_ingestion_completed"] = datetime.now().isoformat()
        self.state["bulk_ingestion_duration"] = str(total_duration)
        self._save_state()

        logger.info("=" * 80)
        logger.info("🎉 BULK HISTORICAL INGESTION COMPLETE!")
        logger.info("=" * 80)
        logger.info(f"⏱️  Total duration: {total_duration}")

        # Summary
        successful = sum(1 for v in results.values() if isinstance(v, int) and v > 0)
        failed = sum(1 for v in results.values() if isinstance(v, str) and v.startswith("ERROR"))
        total_records = sum(v for v in results.values() if isinstance(v, int))

        logger.info(f"📊 Summary: {successful} successful, {failed} failed")
        logger.info(f"📈 Total records ingested: {total_records:,}")
        logger.info("=" * 80)

        return results

    def daily_update(self) -> Dict[str, int]:
        """Perform daily incremental updates - SMART MODE: Focus on missing data types."""
        logger.info("🔄 Starting SMART daily incremental update...")
        logger.info("📊 Skipping companies/prices (already have 4.4K companies, 2.7M prices)")
        logger.info("🎯 Focusing on premium data types with little/no data")

        results = {}

        try:
            # Focus on data types that have little or no data
            logger.info("💰 Ingesting dividend data...")
            results['dividends'] = self.ingest_all_dividends()
            time.sleep(self.batch_delay)

            logger.info("📈 Ingesting margin trading data...")
            results['margin_trading'] = self.ingest_all_margin_trading()
            time.sleep(self.batch_delay)

            logger.info("📊 Ingesting TOPIX data...")
            results['topix_data'] = self.ingest_all_topix_data()
            time.sleep(self.batch_delay)

            logger.info("📈 Ingesting indices data...")
            results['indices_data'] = self.ingest_all_indices_data()
            time.sleep(self.batch_delay)

            logger.info("👥 Ingesting trading by type data...")
            results['trading_by_type'] = self.ingest_all_trading_by_type()
            time.sleep(self.batch_delay)

            logger.info("📉 Ingesting short selling data...")
            results['short_selling'] = self.ingest_all_short_selling()
            time.sleep(self.batch_delay)

            logger.info("🔍 Ingesting breakdown trading data...")
            results['breakdown_trading'] = self.ingest_all_breakdown_trading()
            time.sleep(self.batch_delay)

            logger.info("💼 Ingesting detailed financial statements...")
            results['financial_statements_detailed'] = self.ingest_all_financial_statements_detailed()
            time.sleep(self.batch_delay)

            logger.info("📈 Ingesting futures data...")
            results['futures'] = self.ingest_all_futures()
            time.sleep(self.batch_delay)

            logger.info("📊 Ingesting options data...")
            results['options'] = self.ingest_all_options()
            time.sleep(self.batch_delay)

            logger.info("📈 Ingesting index options data...")
            results['index_options'] = self.ingest_all_index_options()

        except Exception as e:
            logger.error(f"❌ Daily update failed: {e}")
            # Don't re-raise, return partial results
            results['error'] = str(e)

        # Update state
        self.state["last_daily_update"] = datetime.now().isoformat()
        self._save_state()

        logger.info(f"✅ Smart daily update complete: {results}")
        return results

    def run_full_historical_backfill(self) -> Dict[str, int]:
        """Run complete historical data backfill."""
        logger.info("🚀 Starting FULL HISTORICAL BACKFILL")
        logger.info("This will take 24-48 hours to complete!")

        start_time = datetime.now()
        results = {}

        try:
            # Phase 1: Companies
            logger.info("=" * 60)
            logger.info("PHASE 1: COMPANIES DATA")
            logger.info("=" * 60)
            results['companies'] = self.ingest_all_companies()

            # Phase 2: Historical Prices (ALL COMPANIES, ALL HISTORY)
            logger.info("=" * 60)
            logger.info("PHASE 2: HISTORICAL PRICES (ALL COMPANIES)")
            logger.info("=" * 60)
            results['daily_prices'] = self.ingest_all_historical_prices()

            # Phase 3: Financial Statements (ALL COMPANIES)
            logger.info("=" * 60)
            logger.info("PHASE 3: FINANCIAL STATEMENTS (ALL COMPANIES)")
            logger.info("=" * 60)
            results['financial_statements'] = self.ingest_all_financial_statements()

        except Exception as e:
            logger.error(f"❌ Full backfill failed: {e}")
            raise

        finally:
            end_time = datetime.now()
            duration = end_time - start_time

            logger.info("=" * 60)
            logger.info("🎉 FULL HISTORICAL BACKFILL COMPLETE!")
            logger.info("=" * 60)
            logger.info(f"Total Duration: {duration}")
            logger.info(f"Companies: {results.get('companies', 0):,}")
            logger.info(f"Daily Prices: {results.get('daily_prices', 0):,}")
            logger.info(f"Financial Statements: {results.get('financial_statements', 0):,}")
            logger.info("=" * 60)

            # Final state save
            self.state["full_backfill_completed"] = datetime.now().isoformat()
            self._save_state()

        return results

    def get_status(self) -> Dict[str, Any]:
        """Get current ingestion status."""
        return {
            "state": self.state,
            "project_id": Config.GOOGLE_CLOUD_PROJECT,
            "dataset_id": Config.BIGQUERY_DATASET,
            "service_type": "production"
        }


def main():
    """Main CLI interface."""
    import argparse

    parser = argparse.ArgumentParser(description="Production J-Quants Data Ingestion Service")
    parser.add_argument("--mode", choices=[
        "full-backfill",
        "daily-update",
        "companies-only",
        "prices-only",
        "financials-only",
        "dividends-only",
        "margin-trading-only",
        "topix-only",
        "indices-only",
        "trading-by-type-only",
        "short-selling-only",
        "breakdown-trading-only",
        "financial-statements-detailed-only",
        "futures-only",
        "options-only",
        "index-options-only",
        "comprehensive-backfill",
        "ultimate-premium-backfill",
        "status"
    ], required=True, help="Ingestion mode")

    parser.add_argument("--dry-run", action="store_true", help="Show what would be done without executing")

    args = parser.parse_args()

    service = ProductionJQuantsService()

    if args.dry_run:
        logger.info("DRY RUN MODE - No data will be ingested")
        status = service.get_status()
        print(json.dumps(status, indent=2, default=str))
        return

    try:
        if args.mode == "full-backfill":
            logger.info("🚀 STARTING FULL HISTORICAL BACKFILL")
            logger.info("⚠️  This will take 24-48 hours and ingest ~18M records!")
            logger.info("⚠️  Make sure you have sufficient BigQuery quota!")

            # Confirm before starting
            confirm = input("Are you sure you want to proceed? (yes/no): ")
            if confirm.lower() != 'yes':
                logger.info("Aborted by user")
                return

            results = service.run_full_historical_backfill()

        elif args.mode == "daily-update":
            logger.info("🔄 STARTING DAILY UPDATE")
            results = service.daily_update()

        elif args.mode == "companies-only":
            logger.info("🏢 STARTING COMPANIES ONLY")
            results = {"companies": service.ingest_all_companies()}

        elif args.mode == "prices-only":
            logger.info("📈 STARTING HISTORICAL PRICES ONLY")
            results = {"daily_prices": service.ingest_all_historical_prices()}

        elif args.mode == "financials-only":
            logger.info("💰 STARTING FINANCIAL STATEMENTS ONLY")
            results = {"financial_statements": service.ingest_all_financial_statements()}

        elif args.mode == "dividends-only":
            logger.info("💰 STARTING DIVIDENDS ONLY")
            results = {"dividends": service.ingest_all_dividends()}

        elif args.mode == "margin-trading-only":
            logger.info("📈 STARTING MARGIN TRADING ONLY")
            results = {"margin_trading": service.ingest_all_margin_trading()}

        elif args.mode == "topix-only":
            logger.info("📊 STARTING TOPIX ONLY")
            results = {"topix_data": service.ingest_all_topix_data()}

        elif args.mode == "indices-only":
            logger.info("📈 STARTING INDICES ONLY")
            results = {"indices_data": service.ingest_all_indices_data()}

        elif args.mode == "trading-by-type-only":
            logger.info("👥 STARTING TRADING BY TYPE ONLY")
            results = {"trading_by_type": service.ingest_all_trading_by_type()}

        elif args.mode == "short-selling-only":
            logger.info("📉 STARTING SHORT SELLING ONLY")
            results = {"short_selling": service.ingest_all_short_selling()}

        elif args.mode == "breakdown-trading-only":
            logger.info("🔍 STARTING BREAKDOWN TRADING ONLY")
            results = {"breakdown_trading": service.ingest_all_breakdown_trading()}

        elif args.mode == "financial-statements-detailed-only":
            logger.info("💼 STARTING DETAILED FINANCIAL STATEMENTS ONLY")
            results = {"financial_statements_detailed": service.ingest_all_financial_statements_detailed()}

        elif args.mode == "futures-only":
            logger.info("📈 STARTING FUTURES ONLY")
            results = {"futures": service.ingest_all_futures()}

        elif args.mode == "options-only":
            logger.info("📊 STARTING OPTIONS ONLY")
            results = {"options": service.ingest_all_options()}

        elif args.mode == "index-options-only":
            logger.info("📈 STARTING INDEX OPTIONS ONLY")
            results = {"index_options": service.ingest_all_index_options()}

        elif args.mode == "comprehensive-backfill":
            logger.info("🚀 STARTING COMPREHENSIVE BACKFILL (BASIC DATA TYPES)")
            logger.info("⚠️  This will take 24-48 hours and ingest basic data types!")

            results = {}
            results['companies'] = service.ingest_all_companies()
            results['daily_prices'] = service.ingest_all_historical_prices()
            results['financial_statements'] = service.ingest_all_financial_statements()
            results['dividends'] = service.ingest_all_dividends()
            results['margin_trading'] = service.ingest_all_margin_trading()
            results['topix_data'] = service.ingest_all_topix_data()
            results['indices_data'] = service.ingest_all_indices_data()

        elif args.mode == "ultimate-premium-backfill":
            logger.info("🎯 STARTING ULTIMATE PREMIUM BACKFILL (ALL DATA TYPES)")
            logger.info("⚠️  This will take 72-96 hours and ingest EVERYTHING available!")
            logger.info("📊 Premium subscription: Getting ALL historical data from ALL endpoints!")

            results = {}
            # Core data
            results['companies'] = service.ingest_all_companies()
            results['daily_prices'] = service.ingest_all_historical_prices()
            results['financial_statements'] = service.ingest_all_financial_statements()

            # Premium data types
            results['dividends'] = service.ingest_all_dividends()
            results['margin_trading'] = service.ingest_all_margin_trading()
            results['topix_data'] = service.ingest_all_topix_data()
            results['indices_data'] = service.ingest_all_indices_data()
            results['trading_by_type'] = service.ingest_all_trading_by_type()
            results['short_selling'] = service.ingest_all_short_selling()
            results['breakdown_trading'] = service.ingest_all_breakdown_trading()
            results['financial_statements_detailed'] = service.ingest_all_financial_statements_detailed()
            results['futures'] = service.ingest_all_futures()
            results['options'] = service.ingest_all_options()
            results['index_options'] = service.ingest_all_index_options()

        elif args.mode == "status":
            status = service.get_status()
            print(json.dumps(status, indent=2, default=str))
            return

        logger.info("=" * 60)
        logger.info("✅ INGESTION COMPLETE")
        logger.info("=" * 60)
        for data_type, count in results.items():
            if isinstance(count, (int, float)) and count > 0:
                logger.info(f"{data_type}: {count:,} records")
            else:
                logger.info(f"{data_type}: {count} records")
        logger.info("=" * 60)

    except KeyboardInterrupt:
        logger.info("❌ Interrupted by user")
    except Exception as e:
        logger.error(f"❌ Ingestion failed: {e}")
        raise


if __name__ == "__main__":
    main()
