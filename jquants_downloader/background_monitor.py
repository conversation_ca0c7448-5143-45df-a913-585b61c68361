#!/usr/bin/env python3
# ABOUTME: Background monitoring script that runs continuously and logs ingestion progress
# ABOUTME: Designed to run in the background while other work continues

import time
import requests
import subprocess
import sys
from datetime import datetime, timedelta
from google.cloud import bigquery
import json
import signal
import os

class BackgroundMonitor:
    def __init__(self):
        self.client = bigquery.Client(project='tokyotickers')
        self.base_url = "https://jquants-ingestion-621634133093.asia-northeast1.run.app"
        self.log_file = "ingestion_monitor.log"
        self.status_file = "ingestion_status.json"
        self.running = True
        
        # Set up signal handler for clean shutdown
        signal.signal(signal.SIGTERM, self.handle_shutdown)
        signal.signal(signal.SIGINT, self.handle_shutdown)
        
    def handle_shutdown(self, signum, frame):
        """Handle shutdown signals gracefully."""
        self.log("Received shutdown signal, stopping monitor...")
        self.running = False
        
    def log(self, message):
        """Log message to both file and console."""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_message = f"{timestamp} - {message}"
        print(log_message)
        
        with open(self.log_file, 'a') as f:
            f.write(log_message + '\n')
            
    def get_auth_token(self):
        """Get authentication token for Cloud Run service."""
        try:
            result = subprocess.run(
                ['gcloud', 'auth', 'print-identity-token'],
                capture_output=True,
                text=True,
                timeout=10
            )
            if result.returncode == 0:
                return result.stdout.strip()
        except Exception as e:
            self.log(f"Error getting auth token: {e}")
        return None
        
    def check_ingestion_status(self):
        """Check the current ingestion status."""
        token = self.get_auth_token()
        if not token:
            return None
            
        try:
            response = requests.get(
                f"{self.base_url}/status",
                headers={'Authorization': f'Bearer {token}'},
                timeout=30
            )
            
            if response.status_code == 200:
                return response.json()
        except Exception as e:
            self.log(f"Error checking status: {e}")
        return None
        
    def get_data_counts(self):
        """Get current row counts for all tables."""
        counts = {}
        tables = [
            'companies', 'daily_prices', 'dividends', 'financial_statements',
            'margin_trading', 'topix_data', 'indices_data', 'trading_by_type',
            'short_selling', 'breakdown_trading', 'financial_statements_detailed',
            'futures', 'options', 'index_options'
        ]
        
        for table in tables:
            try:
                query = f"SELECT COUNT(*) as count FROM `tokyotickers.jquants_data.{table}`"
                result = list(self.client.query(query))[0]
                counts[table] = result.count
            except Exception:
                counts[table] = 0
                
        return counts
        
    def check_recent_errors(self):
        """Check for recent errors in Cloud Run logs."""
        try:
            result = subprocess.run([
                'gcloud', 'run', 'services', 'logs', 'read', 'jquants-ingestion',
                '--region=asia-northeast1', '--limit=10', '--format=json'
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                logs = json.loads(result.stdout)
                errors = []
                for log in logs:
                    text = log.get('textPayload', '')
                    if 'ERROR' in text or 'FAILED' in text or '429' in text:
                        errors.append({
                            'time': log.get('timestamp', ''),
                            'message': text[:200]
                        })
                return errors[:5]  # Return last 5 errors
        except Exception:
            pass
        return []
        
    def save_status(self, status_data):
        """Save current status to file."""
        with open(self.status_file, 'w') as f:
            json.dump(status_data, f, indent=2, default=str)
            
    def monitor_loop(self):
        """Main monitoring loop."""
        self.log("Starting background ingestion monitor...")
        self.log("Monitoring will check every 2 minutes")
        self.log(f"Logs are being written to: {self.log_file}")
        self.log(f"Current status is saved to: {self.status_file}")
        
        last_counts = {}
        check_interval = 120  # 2 minutes
        
        while self.running:
            try:
                # Get current status
                ingestion_status = self.check_ingestion_status()
                data_counts = self.get_data_counts()
                recent_errors = self.check_recent_errors()
                
                # Calculate changes
                changes = {}
                total_new_records = 0
                for table, count in data_counts.items():
                    if table in last_counts:
                        diff = count - last_counts[table]
                        if diff > 0:
                            changes[table] = diff
                            total_new_records += diff
                            
                # Prepare status summary
                status_summary = {
                    'timestamp': datetime.now(),
                    'ingestion_status': ingestion_status,
                    'total_records': sum(data_counts.values()),
                    'data_counts': data_counts,
                    'recent_changes': changes,
                    'new_records_this_check': total_new_records,
                    'recent_errors': recent_errors
                }
                
                # Save status
                self.save_status(status_summary)
                
                # Log summary
                if total_new_records > 0:
                    self.log(f"✅ Progress detected: {total_new_records:,} new records")
                    for table, diff in changes.items():
                        self.log(f"  - {table}: +{diff:,}")
                else:
                    self.log("⏸️  No new records in this check")
                    
                if recent_errors:
                    self.log(f"⚠️  {len(recent_errors)} recent errors detected")
                    
                # Update last counts
                last_counts = data_counts.copy()
                
                # Wait for next check
                time.sleep(check_interval)
                
            except Exception as e:
                self.log(f"Error in monitoring loop: {e}")
                time.sleep(30)  # Short wait before retry
                
        self.log("Monitor stopped")

def main():
    # Check if already running
    pid_file = "monitor.pid"
    
    if os.path.exists(pid_file):
        print("Monitor may already be running. Check monitor.pid file.")
        print("To stop the existing monitor: kill $(cat monitor.pid)")
        return
        
    # Write PID file
    with open(pid_file, 'w') as f:
        f.write(str(os.getpid()))
        
    try:
        monitor = BackgroundMonitor()
        monitor.monitor_loop()
    finally:
        # Clean up PID file
        if os.path.exists(pid_file):
            os.remove(pid_file)

if __name__ == "__main__":
    main()