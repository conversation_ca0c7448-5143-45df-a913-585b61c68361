"""Configuration settings for J-Quants data downloader."""

import os
from typing import Optional
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


class Config:
    """Configuration class for J-Quants downloader."""

    # J-Quants API Configuration
    JQUANTS_BASE_URL = "https://api.jquants.com/v1"
    JQUANTS_EMAIL = os.getenv("JQUANTS_EMAIL")
    JQUANTS_PASSWORD = os.getenv("JQUANTS_PASSWORD")
    JQUANTS_REFRESH_TOKEN = os.getenv("JQUANTS_REFRESH_TOKEN")

    # Google Cloud Configuration
    GOOGLE_CLOUD_PROJECT = os.getenv("GOOGLE_CLOUD_PROJECT", "tokyotickers")
    GOOGLE_APPLICATION_CREDENTIALS = os.getenv("GOOGLE_APPLICATION_CREDENTIALS")

    # BigQuery Configuration
    BIGQUERY_DATASET = os.getenv("BIGQUERY_DATASET", "jquants_data")
    BIGQUERY_LOCATION = os.getenv("BIGQUERY_LOCATION", "asia-northeast1")  # Tokyo region

    # Table names
    COMPANIES_TABLE = "companies"

    # Logging Configuration
    LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")

    # API Rate Limiting
    API_RATE_LIMIT_CALLS = 100  # calls per minute
    API_RATE_LIMIT_PERIOD = 60  # seconds

    # Retry Configuration
    MAX_RETRIES = 3
    RETRY_DELAY = 1  # seconds

    @classmethod
    def validate(cls) -> None:
        """Validate required configuration."""
        required_vars = [
            ("JQUANTS_EMAIL", cls.JQUANTS_EMAIL),
            ("JQUANTS_PASSWORD", cls.JQUANTS_PASSWORD),
            ("GOOGLE_CLOUD_PROJECT", cls.GOOGLE_CLOUD_PROJECT),
        ]

        missing_vars = [var_name for var_name, var_value in required_vars if not var_value]

        if missing_vars:
            raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")

    @classmethod
    def get_bigquery_table_id(cls, table_name: str) -> str:
        """Get fully qualified BigQuery table ID."""
        return f"{cls.GOOGLE_CLOUD_PROJECT}.{cls.BIGQUERY_DATASET}.{table_name}"
