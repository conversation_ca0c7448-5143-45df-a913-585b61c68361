#!/usr/bin/env python3

import os
from datetime import datetime, timedelta
from jquantsapi import Client

# Initialize J-Quants client
email = os.getenv('JQUANTS_EMAIL', 'and<PERSON><PERSON><PERSON>@gmail.com')
password = os.getenv('JQUANTS_PASSWORD', 'Borboletas747')

client = Client(mail_address=email, password=password)

# Get a small sample of dividend data
end_date = datetime.now()
start_date = end_date - timedelta(days=30)  # Just last 30 days

print(f"Getting dividend data from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")

dividends = client.get_dividend_range(
    start_dt=start_date.strftime('%Y-%m-%d'),
    end_dt=end_date.strftime('%Y-%m-%d')
)

print(f"Type: {type(dividends)}")
print(f"Shape: {dividends.shape if hasattr(dividends, 'shape') else 'No shape'}")

if hasattr(dividends, 'columns'):
    print(f"Columns: {list(dividends.columns)}")
    print(f"Data types:\n{dividends.dtypes}")
    print(f"Sample data:\n{dividends.head()}")
else:
    print(f"Data: {dividends}")
