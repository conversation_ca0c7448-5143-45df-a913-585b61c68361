"""Enhanced J-Quants client with comprehensive data fetching and rate limiting."""

import logging
import time
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
import pandas as pd
from jquantsapi import Client

logger = logging.getLogger(__name__)


class RateLimitedClient:
    """Rate-limited wrapper for J-Quants API client."""
    
    def __init__(self, email: str, password: str, calls_per_minute: int = 30):
        """Initialize rate-limited client.
        
        Args:
            email: J-Quants account email
            password: J-Quants account password
            calls_per_minute: API calls per minute limit
        """
        self.client = Client(mail_address=email, password=password)
        self.calls_per_minute = calls_per_minute
        self.min_interval = 60.0 / calls_per_minute
        self.last_call_time = 0
        
        logger.info(f"Initialized rate-limited J-Quants client ({calls_per_minute} calls/min)")
    
    def _wait_if_needed(self):
        """Enforce rate limiting."""
        current_time = time.time()
        time_since_last_call = current_time - self.last_call_time
        
        if time_since_last_call < self.min_interval:
            sleep_time = self.min_interval - time_since_last_call
            logger.debug(f"Rate limiting: sleeping for {sleep_time:.2f} seconds")
            time.sleep(sleep_time)
        
        self.last_call_time = time.time()
    
    def get_listed_info(self, code: str = "", date_yyyymmdd: str = "") -> pd.DataFrame:
        """Get listed company information with rate limiting."""
        self._wait_if_needed()
        return self.client.get_listed_info(code=code, date_yyyymmdd=date_yyyymmdd)
    
    def get_prices_daily_quotes(self, code: str = "", from_yyyymmdd: str = "", 
                                to_yyyymmdd: str = "", date_yyyymmdd: str = "") -> pd.DataFrame:
        """Get daily price quotes with rate limiting."""
        self._wait_if_needed()
        return self.client.get_prices_daily_quotes(
            code=code, 
            from_yyyymmdd=from_yyyymmdd,
            to_yyyymmdd=to_yyyymmdd,
            date_yyyymmdd=date_yyyymmdd
        )
    
    def get_fins_statements(self, code: str = "", date_yyyymmdd: str = "") -> pd.DataFrame:
        """Get financial statements with rate limiting."""
        self._wait_if_needed()
        return self.client.get_fins_statements(code=code, date_yyyymmdd=date_yyyymmdd)
    
    def get_fins_dividend(self, code: str = "", from_yyyymmdd: str = "",
                         to_yyyymmdd: str = "", date_yyyymmdd: str = "") -> pd.DataFrame:
        """Get dividend information with rate limiting."""
        self._wait_if_needed()
        return self.client.get_fins_dividend(
            code=code,
            from_yyyymmdd=from_yyyymmdd,
            to_yyyymmdd=to_yyyymmdd,
            date_yyyymmdd=date_yyyymmdd
        )
    
    def get_indices_topix(self, from_yyyymmdd: str = "", to_yyyymmdd: str = "") -> pd.DataFrame:
        """Get TOPIX index data with rate limiting."""
        self._wait_if_needed()
        return self.client.get_indices_topix(
            from_yyyymmdd=from_yyyymmdd,
            to_yyyymmdd=to_yyyymmdd
        )
    
    def get_markets_trades_spec(self, section: str = "", from_yyyymmdd: str = "",
                               to_yyyymmdd: str = "") -> pd.DataFrame:
        """Get trading by investor type with rate limiting."""
        self._wait_if_needed()
        return self.client.get_markets_trades_spec(
            section=section,
            from_yyyymmdd=from_yyyymmdd,
            to_yyyymmdd=to_yyyymmdd
        )
    
    def get_markets_breakdown(self, code: str = "", from_yyyymmdd: str = "",
                             to_yyyymmdd: str = "", date_yyyymmdd: str = "") -> pd.DataFrame:
        """Get margin trading breakdown with rate limiting."""
        self._wait_if_needed()
        return self.client.get_markets_breakdown(
            code=code,
            from_yyyymmdd=from_yyyymmdd,
            to_yyyymmdd=to_yyyymmdd,
            date_yyyymmdd=date_yyyymmdd
        )


class ComprehensiveDataFetcher:
    """Comprehensive data fetcher for all J-Quants data types."""
    
    def __init__(self, client: RateLimitedClient):
        """Initialize with rate-limited client."""
        self.client = client
        self.stats = {
            "companies": 0,
            "prices": 0,
            "financials": 0,
            "dividends": 0,
            "topix": 0,
            "trading_by_type": 0,
            "margin_trading": 0
        }
    
    def fetch_all_companies(self, date: Optional[str] = None) -> pd.DataFrame:
        """Fetch all listed companies."""
        logger.info("Fetching all listed companies...")
        
        try:
            df = self.client.get_listed_info(date_yyyymmdd=date or "")
            self.stats["companies"] = len(df)
            logger.info(f"Retrieved {len(df)} companies")
            return df
        except Exception as e:
            logger.error(f"Failed to fetch companies: {e}")
            raise
    
    def fetch_price_history(self, code: str, from_date: str, to_date: str) -> pd.DataFrame:
        """Fetch price history for a specific stock."""
        logger.info(f"Fetching price history for {code} from {from_date} to {to_date}")
        
        try:
            df = self.client.get_prices_daily_quotes(
                code=code,
                from_yyyymmdd=from_date,
                to_yyyymmdd=to_date
            )
            self.stats["prices"] += len(df)
            logger.info(f"Retrieved {len(df)} price records for {code}")
            return df
        except Exception as e:
            logger.error(f"Failed to fetch prices for {code}: {e}")
            raise
    
    def fetch_all_prices_batch(self, codes: List[str], from_date: str, to_date: str,
                              batch_size: int = 10) -> pd.DataFrame:
        """Fetch prices for multiple stocks in batches."""
        all_prices = []
        total_codes = len(codes)
        
        for i in range(0, total_codes, batch_size):
            batch_codes = codes[i:i + batch_size]
            logger.info(f"Processing batch {i//batch_size + 1}/{(total_codes + batch_size - 1)//batch_size}")
            
            for code in batch_codes:
                try:
                    df = self.fetch_price_history(code, from_date, to_date)
                    if not df.empty:
                        all_prices.append(df)
                except Exception as e:
                    logger.warning(f"Skipping {code} due to error: {e}")
                    continue
        
        if all_prices:
            return pd.concat(all_prices, ignore_index=True)
        return pd.DataFrame()
    
    def fetch_financial_statements(self, code: Optional[str] = None, 
                                  date: Optional[str] = None) -> pd.DataFrame:
        """Fetch financial statements."""
        logger.info(f"Fetching financial statements (code={code}, date={date})")
        
        try:
            df = self.client.get_fins_statements(
                code=code or "",
                date_yyyymmdd=date or ""
            )
            self.stats["financials"] += len(df)
            logger.info(f"Retrieved {len(df)} financial statements")
            return df
        except Exception as e:
            logger.error(f"Failed to fetch financial statements: {e}")
            raise
    
    def fetch_dividends(self, code: Optional[str] = None, from_date: Optional[str] = None,
                       to_date: Optional[str] = None) -> pd.DataFrame:
        """Fetch dividend information."""
        logger.info(f"Fetching dividends (code={code}, from={from_date}, to={to_date})")
        
        try:
            df = self.client.get_fins_dividend(
                code=code or "",
                from_yyyymmdd=from_date or "",
                to_yyyymmdd=to_date or ""
            )
            self.stats["dividends"] += len(df)
            logger.info(f"Retrieved {len(df)} dividend records")
            return df
        except Exception as e:
            logger.error(f"Failed to fetch dividends: {e}")
            raise
    
    def fetch_topix_history(self, from_date: str, to_date: str) -> pd.DataFrame:
        """Fetch TOPIX index history."""
        logger.info(f"Fetching TOPIX history from {from_date} to {to_date}")
        
        try:
            df = self.client.get_indices_topix(
                from_yyyymmdd=from_date,
                to_yyyymmdd=to_date
            )
            self.stats["topix"] += len(df)
            logger.info(f"Retrieved {len(df)} TOPIX records")
            return df
        except Exception as e:
            logger.error(f"Failed to fetch TOPIX data: {e}")
            raise
    
    def fetch_trading_by_type(self, section: str, from_date: str, to_date: str) -> pd.DataFrame:
        """Fetch trading data by investor type."""
        logger.info(f"Fetching trading by type for {section} from {from_date} to {to_date}")
        
        try:
            df = self.client.get_markets_trades_spec(
                section=section,
                from_yyyymmdd=from_date,
                to_yyyymmdd=to_date
            )
            self.stats["trading_by_type"] += len(df)
            logger.info(f"Retrieved {len(df)} trading by type records")
            return df
        except Exception as e:
            logger.error(f"Failed to fetch trading by type: {e}")
            raise
    
    def get_statistics(self) -> Dict[str, int]:
        """Get fetching statistics."""
        return self.stats.copy()