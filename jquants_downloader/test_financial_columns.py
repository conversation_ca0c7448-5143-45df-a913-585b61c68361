#!/usr/bin/env python3
"""Test to see what columns are returned by financial statements API."""

from jquantsapi import Client
from dotenv import load_dotenv
import os

def test_financial_columns():
    """Test financial statements API to see column names."""
    load_dotenv()
    
    email = os.getenv("JQUANTS_EMAIL", "").strip('"\'')
    password = os.getenv("JQUANTS_PASSWORD", "").strip('"\'')
    
    print("Testing financial statements API columns...")
    
    try:
        client = Client(mail_address=email, password=password)
        
        # Test with a company that we know has financial data
        statements_df = client.get_fins_statements(code="13010")
        
        if not statements_df.empty:
            print(f"✅ Retrieved {len(statements_df)} financial records")
            print(f"Columns: {list(statements_df.columns)}")
            print("\nFirst few rows:")
            print(statements_df.head())
        else:
            print("❌ No financial data retrieved")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_financial_columns()
