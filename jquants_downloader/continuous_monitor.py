#!/usr/bin/env python3
"""
Continuous monitoring script that:
1. Checks data ingestion progress every 2 minutes
2. Monitors for errors and service health
3. Automatically retriggers if service stops
4. Provides real-time status updates
"""

import time
import requests
import subprocess
from datetime import datetime, timedelta
from google.cloud import bigquery
import logging

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ContinuousMonitor:
    def __init__(self):
        self.client = bigquery.Client(project='tokyotickers')
        self.service_url = "https://jquants-ingestion-621634133093.asia-northeast1.run.app/trigger"
        self.last_data_counts = {}
        self.last_check_time = None
        self.consecutive_no_progress = 0
        self.max_no_progress = 6  # 12 minutes of no progress before retrigger
        
    def get_current_data_counts(self):
        """Get current row counts for all tables."""
        counts = {}
        tables = ['companies', 'daily_prices', 'dividends', 'margin_trading', 'topix_data', 
                 'indices_data', 'trading_by_type', 'short_selling', 'breakdown_trading']
        
        for table in tables:
            try:
                query = f"SELECT COUNT(*) as count FROM `tokyotickers.jquants_data.{table}`"
                result = list(self.client.query(query))[0]
                counts[table] = result.count
            except Exception as e:
                if "not found" in str(e).lower():
                    counts[table] = 0
                else:
                    logger.warning(f"Error checking {table}: {e}")
                    counts[table] = -1
        
        return counts
    
    def check_recent_logs(self):
        """Check for recent errors in Cloud Run logs."""
        try:
            result = subprocess.run([
                'gcloud', 'run', 'services', 'logs', 'read', 'jquants-ingestion',
                '--region=asia-northeast1', '--limit=5'
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                logs = result.stdout
                if "ERROR" in logs or "FAILED" in logs:
                    # Extract the last error
                    lines = logs.split('\n')
                    for line in reversed(lines):
                        if "ERROR" in line or "FAILED" in line:
                            return line.strip()
            return None
        except Exception as e:
            logger.warning(f"Could not check logs: {e}")
            return None
    
    def get_auth_token(self):
        """Get authentication token for Cloud Run service."""
        try:
            result = subprocess.run(
                ['gcloud', 'auth', 'print-identity-token'],
                capture_output=True,
                text=True,
                timeout=10
            )
            if result.returncode == 0:
                return result.stdout.strip()
            else:
                logger.error(f"Failed to get auth token: {result.stderr}")
                return None
        except Exception as e:
            logger.error(f"Error getting auth token: {e}")
            return None
    
    def trigger_ingestion(self):
        """Trigger the ingestion service."""
        try:
            # Get authentication token
            token = self.get_auth_token()
            if not token:
                logger.error("❌ Could not get authentication token")
                return False
            
            response = requests.post(
                self.service_url,
                headers={
                    'Authorization': f'Bearer {token}',
                    'Content-Type': 'application/json'
                },
                json={"mode": "daily-update"},
                timeout=30
            )
            if response.status_code == 200:
                logger.info("✅ Successfully triggered ingestion service")
                return True
            else:
                logger.error(f"❌ Failed to trigger service: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            logger.error(f"❌ Error triggering service: {e}")
            return False
    
    def print_status(self, counts, progress_detected, error_msg=None):
        """Print current status in a nice format."""
        now = datetime.now().strftime('%H:%M:%S')
        print(f"\n📊 MONITORING UPDATE - {now}")
        print("=" * 60)
        
        # Data counts
        total_records = sum(count for count in counts.values() if count > 0)
        print(f"📋 Data Status (Total: {total_records:,} records):")
        
        for table, count in counts.items():
            if count > 0:
                # Check for progress
                if table in self.last_data_counts:
                    diff = count - self.last_data_counts[table]
                    if diff > 0:
                        print(f"  {table:<20} {count:>10,} (+{diff:,}) 📈")
                    else:
                        print(f"  {table:<20} {count:>10,}")
                else:
                    print(f"  {table:<20} {count:>10,}")
            elif count == 0:
                print(f"  {table:<20}       No data")
        
        # Progress status
        if progress_detected:
            print("🔄 Status: ACTIVE INGESTION")
            self.consecutive_no_progress = 0
        else:
            self.consecutive_no_progress += 1
            print(f"⏸️  Status: NO PROGRESS ({self.consecutive_no_progress}/{self.max_no_progress})")
        
        # Error status
        if error_msg:
            print(f"❌ Recent Error: {error_msg}")
        
        print("=" * 60)
    
    def run_monitoring_loop(self):
        """Main monitoring loop."""
        logger.info("🚀 Starting continuous monitoring...")
        logger.info("📊 Checking data every 2 minutes")
        logger.info("🔄 Will retrigger if no progress for 12 minutes")
        logger.info("❌ Press Ctrl+C to stop")
        
        try:
            while True:
                # Get current data counts
                current_counts = self.get_current_data_counts()
                
                # Check for progress
                progress_detected = False
                if self.last_data_counts:
                    for table, count in current_counts.items():
                        if count > self.last_data_counts.get(table, 0):
                            progress_detected = True
                            break
                
                # Check for errors
                error_msg = self.check_recent_logs()
                
                # Print status
                self.print_status(current_counts, progress_detected, error_msg)
                
                # Check if we need to retrigger
                if (not progress_detected and 
                    self.consecutive_no_progress >= self.max_no_progress and
                    self.last_data_counts):  # Don't retrigger on first run
                    
                    logger.warning("🚨 No progress detected for 12+ minutes, retriggering...")
                    if self.trigger_ingestion():
                        self.consecutive_no_progress = 0
                        logger.info("✅ Service retriggered, monitoring continues...")
                    else:
                        logger.error("❌ Failed to retrigger, will try again in 2 minutes")
                
                # Update state
                self.last_data_counts = current_counts.copy()
                self.last_check_time = datetime.now()
                
                # Wait 2 minutes
                logger.info("⏳ Waiting 2 minutes for next check...")
                time.sleep(120)
                
        except KeyboardInterrupt:
            logger.info("👋 Monitoring stopped by user")
        except Exception as e:
            logger.error(f"❌ Monitoring error: {e}")
            raise

def main():
    monitor = ContinuousMonitor()
    monitor.run_monitoring_loop()

if __name__ == "__main__":
    main()