#!/usr/bin/env python3
# ABOUTME: Quick script to check current ingestion status and data counts
# ABOUTME: Provides a snapshot of the current state without continuous monitoring

import subprocess
import requests
import json
from datetime import datetime
from google.cloud import bigquery
from tabulate import tabulate

def get_auth_token():
    """Get authentication token."""
    try:
        result = subprocess.run(
            ['gcloud', 'auth', 'print-identity-token'],
            capture_output=True,
            text=True,
            timeout=10
        )
        if result.returncode == 0:
            return result.stdout.strip()
    except Exception:
        pass
    return None

def check_service_status():
    """Check Cloud Run service status."""
    token = get_auth_token()
    if not token:
        return None
        
    base_url = "https://jquants-ingestion-621634133093.asia-northeast1.run.app"
    
    try:
        # Check health
        health_resp = requests.get(
            f"{base_url}/",
            headers={'Authorization': f'Bearer {token}'},
            timeout=10
        )
        
        # Check status
        status_resp = requests.get(
            f"{base_url}/status",
            headers={'Authorization': f'Bearer {token}'},
            timeout=10
        )
        
        return {
            'health': health_resp.status_code == 200,
            'status': status_resp.json() if status_resp.status_code == 200 else None
        }
    except Exception as e:
        return {'health': False, 'error': str(e)}

def get_data_summary():
    """Get summary of data in BigQuery."""
    client = bigquery.Client(project='tokyotickers')
    
    tables_info = []
    total_records = 0
    
    tables = [
        ('companies', 'Company master data'),
        ('daily_prices', 'Daily stock prices'),
        ('dividends', 'Dividend data'),
        ('financial_statements', 'Financial statements'),
        ('margin_trading', 'Margin trading data'),
        ('topix_data', 'TOPIX index data'),
        ('indices_data', 'Other indices data'),
        ('trading_by_type', 'Trading by investor type'),
        ('short_selling', 'Short selling data'),
        ('breakdown_trading', 'Trading breakdown'),
        ('futures', 'Futures data'),
        ('options', 'Options data'),
    ]
    
    for table_name, description in tables:
        try:
            # Get count
            count_query = f"SELECT COUNT(*) as count FROM `tokyotickers.jquants_data.{table_name}`"
            count_result = list(client.query(count_query))[0]
            count = count_result.count
            
            # Get latest date if applicable
            latest_date = None
            if table_name in ['daily_prices', 'margin_trading', 'topix_data']:
                date_query = f"""
                SELECT MAX(date) as latest_date 
                FROM `tokyotickers.jquants_data.{table_name}`
                """
                date_result = list(client.query(date_query))
                if date_result and date_result[0].latest_date:
                    latest_date = date_result[0].latest_date.strftime('%Y-%m-%d')
            
            tables_info.append({
                'Table': table_name,
                'Records': f"{count:,}" if count > 0 else "No data",
                'Latest Date': latest_date or "-",
                'Description': description
            })
            
            if count > 0:
                total_records += count
                
        except Exception as e:
            tables_info.append({
                'Table': table_name,
                'Records': "Error",
                'Latest Date': "-",
                'Description': str(e)[:50]
            })
    
    return tables_info, total_records

def check_recent_activity():
    """Check recent Cloud Run logs for activity."""
    try:
        result = subprocess.run([
            'gcloud', 'run', 'services', 'logs', 'read', 'jquants-ingestion',
            '--region=asia-northeast1', '--limit=5', '--format=json'
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            logs = json.loads(result.stdout)
            recent_activities = []
            
            for log in logs:
                text = log.get('textPayload', '')
                timestamp = log.get('timestamp', '')
                
                # Look for interesting activities
                if any(keyword in text for keyword in ['Ingested', 'Fetching', 'Starting', 'ERROR', 'WARNING']):
                    recent_activities.append({
                        'time': timestamp[:19] if timestamp else '',
                        'message': text[:100]
                    })
                    
            return recent_activities[:5]
    except Exception:
        pass
    return []

def main():
    print("=" * 80)
    print("J-QUANTS INGESTION STATUS CHECK")
    print("=" * 80)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Check service status
    print("🔍 Checking Cloud Run Service...")
    service_status = check_service_status()
    
    if service_status:
        if service_status.get('health'):
            print("✅ Service is healthy")
            
            if service_status.get('status'):
                status_data = service_status['status']
                if status_data.get('status') == 'success':
                    state = status_data.get('data', {}).get('state', {})
                    print(f"   Service Type: {status_data.get('data', {}).get('service_type', 'unknown')}")
                    
                    # Show last update times
                    companies_updated = state.get('companies_last_updated')
                    prices_date = state.get('prices_last_date')
                    
                    if companies_updated:
                        print(f"   Companies Last Updated: {companies_updated}")
                    if prices_date:
                        print(f"   Prices Last Date: {prices_date}")
        else:
            print("❌ Service is not responding")
            if service_status.get('error'):
                print(f"   Error: {service_status['error']}")
    else:
        print("❌ Could not check service status")
    
    print()
    
    # Get data summary
    print("📊 BigQuery Data Summary:")
    tables_info, total_records = get_data_summary()
    
    if tables_info:
        print(tabulate(tables_info, headers='keys', tablefmt='grid'))
        print(f"\n📈 Total Records: {total_records:,}")
    
    # Check recent activity
    print("\n📜 Recent Activity:")
    activities = check_recent_activity()
    
    if activities:
        for activity in activities:
            print(f"   {activity['time']} - {activity['message']}")
    else:
        print("   No recent activity found")
    
    print("\n" + "=" * 80)
    
    # Check if background monitor is running
    if os.path.exists('monitor.pid'):
        with open('monitor.pid', 'r') as f:
            pid = f.read().strip()
        print(f"ℹ️  Background monitor is running (PID: {pid})")
        print("   Check ingestion_monitor.log for detailed logs")
        print("   Check ingestion_status.json for latest status")
    else:
        print("ℹ️  Background monitor is not running")
        print("   Run: python background_monitor.py & ")

if __name__ == "__main__":
    import os
    main()