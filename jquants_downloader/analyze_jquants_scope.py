#!/usr/bin/env python3
"""Analyze the full scope of J-Quants data to plan comprehensive ingestion."""

from jquantsapi import Client
from dotenv import load_dotenv
import os
import pandas as pd
from datetime import datetime, <PERSON><PERSON><PERSON>

def analyze_jquants_scope():
    """Analyze the full scope of available J-Quants data."""
    load_dotenv()
    
    email = os.getenv("JQUANTS_EMAIL", "").strip('"\'')
    password = os.getenv("JQUANTS_PASSWORD", "").strip('"\'')
    
    print("🔍 ANALYZING FULL J-QUANTS DATA SCOPE")
    print("=" * 60)
    
    try:
        client = Client(mail_address=email, password=password)
        
        # 1. Get all companies first
        print("1. 📊 COMPANIES ANALYSIS")
        print("-" * 30)
        companies_df = client.get_listed_info()
        print(f"Total Companies: {len(companies_df):,}")
        
        # Analyze by market
        if 'MarketCodeName' in companies_df.columns:
            market_counts = companies_df['MarketCodeName'].value_counts()
            print("Companies by Market:")
            for market, count in market_counts.head(10).items():
                print(f"  {market}: {count:,}")
        
        # Get sample of company codes for testing
        sample_codes = companies_df['Code'].head(10).tolist()
        print(f"Sample codes for testing: {sample_codes[:5]}")
        
        # 2. Analyze daily prices scope
        print("\n2. 📈 DAILY PRICES ANALYSIS")
        print("-" * 30)
        
        # Test with one company to understand date range
        test_code = sample_codes[0]
        print(f"Testing with company: {test_code}")
        
        # Try to get historical data
        try:
            # Test recent data
            recent_prices = client.get_prices_daily_quotes(code=test_code)
            if not recent_prices.empty:
                print(f"Available price records for {test_code}: {len(recent_prices):,}")
                print(f"Date range: {recent_prices['Date'].min()} to {recent_prices['Date'].max()}")
            else:
                print(f"No price data available for {test_code}")
        except Exception as e:
            print(f"Error getting prices for {test_code}: {e}")
        
        # 3. Analyze financial statements scope
        print("\n3. 💰 FINANCIAL STATEMENTS ANALYSIS")
        print("-" * 30)
        
        financial_companies = []
        for i, code in enumerate(sample_codes[:5]):
            try:
                statements = client.get_fins_statements(code=code)
                if not statements.empty:
                    financial_companies.append({
                        'code': code,
                        'records': len(statements),
                        'date_range': f"{statements['DisclosedDate'].min()} to {statements['DisclosedDate'].max()}"
                    })
                    print(f"  {code}: {len(statements):,} financial records")
                else:
                    print(f"  {code}: No financial data")
            except Exception as e:
                print(f"  {code}: Error - {e}")
        
        # 4. Estimate total data volume
        print("\n4. 📊 ESTIMATED TOTAL DATA VOLUME")
        print("-" * 30)
        
        total_companies = len(companies_df)
        
        # Estimate daily prices (assuming 5 years of data, 250 trading days per year)
        estimated_price_records = total_companies * 250 * 5
        print(f"Estimated daily price records (5 years): {estimated_price_records:,}")
        
        # Estimate financial statements (assuming quarterly reports for 5 years)
        companies_with_financials = len(financial_companies)
        if companies_with_financials > 0:
            avg_financial_records = sum(fc['records'] for fc in financial_companies) / companies_with_financials
            estimated_financial_records = total_companies * avg_financial_records
            print(f"Estimated financial statement records: {estimated_financial_records:,.0f}")
        
        # 5. Recommended ingestion strategy
        print("\n5. 🚀 RECOMMENDED INGESTION STRATEGY")
        print("-" * 30)
        print("Phase 1: Historical Data Backfill")
        print("  - All companies: 4,412 companies")
        print("  - Daily prices: 5 years of historical data")
        print("  - Financial statements: All available historical data")
        print("  - Estimated time: 24-48 hours with rate limiting")
        print()
        print("Phase 2: Daily Updates")
        print("  - Daily price updates for all companies")
        print("  - New financial statements as they're published")
        print("  - Estimated time: 30-60 minutes daily")
        print()
        print("Phase 3: Real-time Monitoring")
        print("  - Monitor for new companies")
        print("  - Detect and backfill any missing data")
        print("  - Data quality validation")
        
        return {
            'total_companies': total_companies,
            'sample_codes': sample_codes,
            'financial_companies': financial_companies
        }
        
    except Exception as e:
        print(f"❌ Error analyzing scope: {e}")
        return None

if __name__ == "__main__":
    result = analyze_jquants_scope()
    if result:
        print(f"\n✅ Analysis complete. Ready to build comprehensive ingestion service.")
    else:
        print(f"\n❌ Analysis failed.")
