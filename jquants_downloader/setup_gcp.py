#!/usr/bin/env python3
"""Script to help set up Google Cloud Platform for the J-Quants project."""

import subprocess
import sys
import json
from typing import List, Dict, Any, Optional


class GCPSetup:
    """Helper class for setting up GCP project and services."""

    def __init__(self, project_id: str, gcp_account: str, billing_account: Optional[str] = None, region: str = "asia-northeast1"):
        """Initialize GCP setup.

        Args:
            project_id: GCP project ID to create/use
            gcp_account: GCP account email
            billing_account: Billing account ID (optional)
            region: GCP region to use (default: asia-northeast1 for Tokyo)
        """
        self.project_id = project_id
        self.gcp_account = gcp_account
        self.billing_account = billing_account
        self.region = region

    def run_command(self, command: List[str], check: bool = True) -> subprocess.CompletedProcess:
        """Run a shell command and return the result.

        Args:
            command: Command to run as list of strings
            check: Whether to raise exception on non-zero exit code

        Returns:
            CompletedProcess object
        """
        print(f"Running: {' '.join(command)}")
        try:
            result = subprocess.run(command, capture_output=True, text=True, check=check)
            if result.stdout:
                print(f"Output: {result.stdout.strip()}")
            return result
        except subprocess.CalledProcessError as e:
            print(f"Error: {e}")
            if e.stderr:
                print(f"Error output: {e.stderr.strip()}")
            raise

    def check_gcloud_auth(self) -> bool:
        """Check if gcloud is authenticated.

        Returns:
            True if authenticated, False otherwise
        """
        try:
            result = self.run_command(["gcloud", "auth", "list", "--format=json"], check=False)
            if result.returncode == 0:
                accounts = json.loads(result.stdout)
                active_accounts = [acc for acc in accounts if acc.get("status") == "ACTIVE"]
                return len(active_accounts) > 0
        except Exception:
            pass
        return False

    def authenticate_gcloud(self) -> None:
        """Authenticate with specific gcloud account."""
        # Check if the specific account is already authenticated
        try:
            result = self.run_command(["gcloud", "auth", "list", "--format=json"], check=False)
            if result.returncode == 0:
                accounts = json.loads(result.stdout)
                account_emails = [acc.get("account") for acc in accounts]

                if self.gcp_account in account_emails:
                    print(f"Account {self.gcp_account} is already authenticated")
                    # Activate the account
                    self.run_command(["gcloud", "config", "set", "account", self.gcp_account])
                    return
        except Exception:
            pass

        print(f"Authenticating with gcloud account: {self.gcp_account}")
        self.run_command(["gcloud", "auth", "login", "--account", self.gcp_account])
        print("Authentication completed")

    def create_project(self) -> None:
        """Create GCP project."""
        print(f"Creating project: {self.project_id}")

        # Check if project already exists
        try:
            result = self.run_command(
                ["gcloud", "projects", "describe", self.project_id, "--format=json"],
                check=False
            )
            if result.returncode == 0:
                print(f"Project {self.project_id} already exists")
                return
        except Exception:
            pass

        # Create the project
        create_cmd = ["gcloud", "projects", "create", self.project_id]
        if self.billing_account:
            create_cmd.extend(["--billing-project", self.billing_account])

        self.run_command(create_cmd)
        print(f"Project {self.project_id} created successfully")

    def set_project(self) -> None:
        """Set the current gcloud project."""
        print(f"Setting current project to: {self.project_id}")
        self.run_command(["gcloud", "config", "set", "project", self.project_id])

    def link_billing_account(self) -> None:
        """Link billing account to the project."""
        if not self.billing_account:
            print("No billing account specified, skipping billing setup")
            print("Note: You'll need to manually link a billing account to enable APIs")
            return

        print(f"Linking billing account {self.billing_account} to project {self.project_id}")
        self.run_command([
            "gcloud", "billing", "projects", "link", self.project_id,
            "--billing-account", self.billing_account
        ])

    def enable_apis(self) -> None:
        """Enable required APIs."""
        apis = [
            "bigquery.googleapis.com",
            "storage.googleapis.com",
            "iam.googleapis.com",
            "cloudresourcemanager.googleapis.com"
        ]

        print("Enabling required APIs...")
        for api in apis:
            print(f"Enabling {api}")
            self.run_command(["gcloud", "services", "enable", api])

        print("All APIs enabled successfully")

    def create_service_account(self) -> str:
        """Create service account for the application.

        Returns:
            Service account email
        """
        service_account_name = "jquants-pipeline"
        service_account_email = f"{service_account_name}@{self.project_id}.iam.gserviceaccount.com"

        print(f"Creating service account: {service_account_name}")

        # Check if service account already exists
        try:
            result = self.run_command([
                "gcloud", "iam", "service-accounts", "describe", service_account_email,
                "--format=json"
            ], check=False)
            if result.returncode == 0:
                print(f"Service account {service_account_email} already exists")
                return service_account_email
        except Exception:
            pass

        # Create service account
        self.run_command([
            "gcloud", "iam", "service-accounts", "create", service_account_name,
            "--display-name", "J-Quants Data Pipeline Service Account",
            "--description", "Service account for J-Quants to BigQuery data pipeline"
        ])

        print(f"Service account {service_account_email} created successfully")
        return service_account_email

    def grant_permissions(self, service_account_email: str) -> None:
        """Grant necessary permissions to the service account.

        Args:
            service_account_email: Email of the service account
        """
        roles = [
            "roles/bigquery.admin",
            "roles/storage.admin",
        ]

        print("Granting permissions to service account...")
        for role in roles:
            print(f"Granting {role}")
            self.run_command([
                "gcloud", "projects", "add-iam-policy-binding", self.project_id,
                "--member", f"serviceAccount:{service_account_email}",
                "--role", role
            ])

        print("Permissions granted successfully")

    def create_service_account_key(self, service_account_email: str, key_file: str) -> None:
        """Create and download service account key.

        Args:
            service_account_email: Email of the service account
            key_file: Path to save the key file
        """
        print(f"Creating service account key: {key_file}")

        self.run_command([
            "gcloud", "iam", "service-accounts", "keys", "create", key_file,
            "--iam-account", service_account_email
        ])

        print(f"Service account key saved to: {key_file}")
        print(f"Set GOOGLE_APPLICATION_CREDENTIALS={key_file} in your environment")

    def setup_complete_project(self, key_file: str = "service-account-key.json") -> None:
        """Run complete project setup.

        Args:
            key_file: Path to save the service account key file
        """
        print(f"Starting complete GCP setup for project: {self.project_id}")

        try:
            # Step 1: Authenticate
            self.authenticate_gcloud()

            # Step 2: Create project
            self.create_project()

            # Step 3: Set current project
            self.set_project()

            # Step 4: Link billing (if provided)
            self.link_billing_account()

            # Step 5: Enable APIs
            self.enable_apis()

            # Step 6: Create service account
            service_account_email = self.create_service_account()

            # Step 7: Grant permissions
            self.grant_permissions(service_account_email)

            # Step 8: Create service account key
            self.create_service_account_key(service_account_email, key_file)

            print("\n" + "="*60)
            print("GCP SETUP COMPLETED SUCCESSFULLY!")
            print("="*60)
            print(f"GCP Account: {self.gcp_account}")
            print(f"Project ID: {self.project_id}")
            print(f"Region: {self.region}")
            print(f"Service Account: {service_account_email}")
            print(f"Key File: {key_file}")
            print("\nNext steps:")
            print(f"1. Set environment variable: export GOOGLE_APPLICATION_CREDENTIALS={key_file}")
            print("2. Create environment configuration:")
            print(f"   python env_manager.py create <env_name> {self.project_id} {self.gcp_account}")
            print("3. Switch to the environment:")
            print(f"   python env_manager.py switch <env_name>")
            print("4. Update .env with your J-Quants credentials")
            print("5. Run: python main.py --setup-only")
            print("6. Run: python main.py")

        except Exception as e:
            print(f"\nSetup failed: {e}")
            sys.exit(1)


def main():
    """Main function for GCP setup script."""
    import argparse

    parser = argparse.ArgumentParser(description="Set up GCP project for J-Quants pipeline")
    parser.add_argument("--project-id", default="tokyo_tickers", help="GCP project ID")
    parser.add_argument("--gcp-account", default="<EMAIL>", help="GCP account email")
    parser.add_argument("--billing-account", help="Billing account ID (optional)")
    parser.add_argument("--region", default="asia-northeast1", help="GCP region (default: asia-northeast1 for Tokyo)")
    parser.add_argument("--key-file", default="service-account-key.json", help="Service account key file path")

    args = parser.parse_args()

    setup = GCPSetup(args.project_id, args.gcp_account, args.billing_account, args.region)
    setup.setup_complete_project(args.key_file)


if __name__ == "__main__":
    main()
