"""J-Quants API client wrapper using the official jquantsapi library."""

import logging
from typing import Optional, List
import pandas as pd
from jquantsapi import Client

from models import ListedCompany

logger = logging.getLogger(__name__)


class JQuantsAPIError(Exception):
    """Custom exception for J-Quants API errors."""
    pass


class JQuantsClient:
    """Wrapper client for the official J-Quants API client."""

    def __init__(self, email: str, password: str, refresh_token: Optional[str] = None):
        """Initialize the J-Quants client wrapper.

        Args:
            email: J-Quants account email
            password: J-Quants account password
            refresh_token: Optional refresh token (not used with official client)
        """
        self.email = email
        self.password = password
        self.refresh_token = refresh_token

        # Initialize the official J-Quants client
        self.client = Client(mail_address=email, password=password)

        logger.info("J-Quants client initialized using official jquantsapi library")

    def get_listed_companies(self, date: Optional[str] = None, code: Optional[str] = None) -> List[ListedCompany]:
        """Get listed companies information.

        Args:
            date: Date of application of information (YYYY-MM-DD or YYYYMMDD)
            code: Issue code to filter by

        Returns:
            List of ListedCompany objects
        """
        logger.info(f"Getting listed companies data (date={date}, code={code})")

        try:
            # Use the official client to get listed companies
            companies_df = self.client.get_listed_info(code=code or "", date_yyyymmdd=date or "")

            logger.info(f"Successfully retrieved {len(companies_df)} companies")

            # Convert DataFrame to ListedCompany objects
            companies = []
            for _, row in companies_df.iterrows():
                company = ListedCompany(
                    date=row['Date'],
                    code=row['Code'],
                    company_name=row['CompanyName'],
                    company_name_english=row.get('CompanyNameEnglish', ''),
                    sector17_code=row.get('Sector17Code', ''),
                    sector17_code_name=row.get('Sector17CodeName', ''),
                    sector33_code=row.get('Sector33Code', ''),
                    sector33_code_name=row.get('Sector33CodeName', ''),
                    scale_category=row.get('ScaleCategory', ''),
                    market_code=row.get('MarketCode', ''),
                    market_code_name=row.get('MarketCodeName', ''),
                    margin_code=row.get('MarginCode', ''),
                    margin_code_name=row.get('MarginCodeName', '')
                )
                companies.append(company)

            return companies

        except Exception as e:
            logger.error(f"Failed to get listed companies: {e}")
            raise JQuantsAPIError(f"Failed to get listed companies: {e}")

    def ensure_authenticated(self) -> None:
        """Ensure client is authenticated (handled automatically by official client)."""
        # The official client handles authentication automatically
        pass