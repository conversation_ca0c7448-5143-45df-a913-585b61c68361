#!/usr/bin/env python3
"""Quick BigQuery monitoring - shows table status and recent jobs."""

from google.cloud import bigquery
from datetime import datetime

def quick_monitor():
    client = bigquery.Client(project='tokyotickers')
    
    print("\n📊 JQUANTS DATA STATUS")
    print("="*60)
    
    # Quick table stats
    query = """
    SELECT 
        table_id as table_name,
        row_count,
        ROUND(size_bytes / POW(10, 9), 2) as size_gb
    FROM `tokyotickers.jquants_data.__TABLES__`
    ORDER BY row_count DESC
    """
    
    print("\n📋 Table Statistics:")
    for row in client.query(query):
        print(f"  {row.table_name:<25} {row.row_count:>12,} rows ({row.size_gb:.2f} GB)")
    
    # Recent jobs summary
    query = """
    SELECT 
        CASE WHEN error_result IS NULL THEN 'SUCCESS' ELSE 'FAILED' END as status,
        COUNT(*) as count
    FROM `tokyotickers.region-asia-northeast1.INFORMATION_SCHEMA.JOBS_BY_PROJECT`
    WHERE creation_time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 24 HOUR)
        AND job_type = 'LOAD'
        AND destination_table.dataset_id = 'jquants_data'
    GROUP BY status
    """
    
    print("\n💼 Jobs (Last 24h):")
    for row in client.query(query):
        print(f"  {row.status}: {row.count}")
    
    # Latest data dates
    print("\n📅 Latest Data:")
    tables = [
        ('daily_prices', 'date'),
        ('dividends', 'AnnouncementDate'),
        ('financial_statements', 'disclosure_date'),
        ('margin_trading', 'Date'),
        ('topix_data', 'Date')
    ]
    
    for table, date_col in tables:
        try:
            query = f"SELECT MAX({date_col}) as latest FROM `tokyotickers.jquants_data.{table}`"
            result = list(client.query(query))[0]
            print(f"  {table:<25} {result.latest}")
        except:
            print(f"  {table:<25} No data")
    
    print("\n✅ Quick check complete")
    print("="*60)

if __name__ == "__main__":
    quick_monitor()