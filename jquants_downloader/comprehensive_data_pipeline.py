#!/usr/bin/env python3
"""Comprehensive J-Quants data pipeline for all endpoints."""

import logging
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List
import pandas as pd
from jquantsapi import Client
from dotenv import load_dotenv
import os

from bigquery_client import BigQueryClient
from config import Config

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ComprehensiveJQuantsPipeline:
    """Comprehensive pipeline for ingesting all J-Quants data."""

    def __init__(self):
        """Initialize the comprehensive pipeline."""
        load_dotenv()

        # Initialize clients
        self.jquants_client = Client(
            mail_address=Config.JQUANTS_EMAIL,
            password=Config.JQUANTS_PASSWORD
        )
        self.bigquery_client = BigQueryClient(
            project_id=Config.GOOGLE_CLOUD_PROJECT,
            dataset_id=Config.BIGQUERY_DATASET
        )

        # Rate limiting settings
        self.request_delay = 2  # seconds between requests
        self.batch_delay = 10   # seconds between batches

        logger.info("Comprehensive J-Quants pipeline initialized")

    def create_table_schemas(self) -> Dict[str, List[Dict[str, Any]]]:
        """Define BigQuery table schemas for all data types."""
        schemas = {
            # 1. Listed Companies (already exists)
            'companies': [
                {'name': 'date', 'type': 'DATE', 'mode': 'REQUIRED'},
                {'name': 'code', 'type': 'STRING', 'mode': 'REQUIRED'},
                {'name': 'company_name', 'type': 'STRING', 'mode': 'NULLABLE'},
                {'name': 'company_name_english', 'type': 'STRING', 'mode': 'NULLABLE'},
                {'name': 'sector17_code', 'type': 'STRING', 'mode': 'NULLABLE'},
                {'name': 'sector17_code_name', 'type': 'STRING', 'mode': 'NULLABLE'},
                {'name': 'sector33_code', 'type': 'STRING', 'mode': 'NULLABLE'},
                {'name': 'sector33_code_name', 'type': 'STRING', 'mode': 'NULLABLE'},
                {'name': 'scale_category', 'type': 'STRING', 'mode': 'NULLABLE'},
                {'name': 'market_code', 'type': 'STRING', 'mode': 'NULLABLE'},
                {'name': 'market_code_name', 'type': 'STRING', 'mode': 'NULLABLE'},
                {'name': 'margin_code', 'type': 'STRING', 'mode': 'NULLABLE'},
                {'name': 'margin_code_name', 'type': 'STRING', 'mode': 'NULLABLE'},
            ],

            # 2. Daily Stock Prices
            'daily_prices': [
                {'name': 'date', 'type': 'DATE', 'mode': 'REQUIRED'},
                {'name': 'code', 'type': 'STRING', 'mode': 'REQUIRED'},
                {'name': 'open', 'type': 'FLOAT', 'mode': 'NULLABLE'},
                {'name': 'high', 'type': 'FLOAT', 'mode': 'NULLABLE'},
                {'name': 'low', 'type': 'FLOAT', 'mode': 'NULLABLE'},
                {'name': 'close', 'type': 'FLOAT', 'mode': 'NULLABLE'},
                {'name': 'volume', 'type': 'INTEGER', 'mode': 'NULLABLE'},
                {'name': 'turnover_value', 'type': 'FLOAT', 'mode': 'NULLABLE'},
                {'name': 'adjustment_factor', 'type': 'FLOAT', 'mode': 'NULLABLE'},
                {'name': 'adjustment_open', 'type': 'FLOAT', 'mode': 'NULLABLE'},
                {'name': 'adjustment_high', 'type': 'FLOAT', 'mode': 'NULLABLE'},
                {'name': 'adjustment_low', 'type': 'FLOAT', 'mode': 'NULLABLE'},
                {'name': 'adjustment_close', 'type': 'FLOAT', 'mode': 'NULLABLE'},
                {'name': 'adjustment_volume', 'type': 'INTEGER', 'mode': 'NULLABLE'},
            ],

            # 3. Financial Statements
            'financial_statements': [
                {'name': 'date', 'type': 'DATE', 'mode': 'REQUIRED'},
                {'name': 'disclosure_date', 'type': 'DATE', 'mode': 'NULLABLE'},
                {'name': 'code', 'type': 'STRING', 'mode': 'REQUIRED'},
                {'name': 'local_code', 'type': 'STRING', 'mode': 'NULLABLE'},
                {'name': 'disclosure_number', 'type': 'STRING', 'mode': 'NULLABLE'},
                {'name': 'type_of_document', 'type': 'STRING', 'mode': 'NULLABLE'},
                {'name': 'type_of_current_period', 'type': 'STRING', 'mode': 'NULLABLE'},
                {'name': 'current_period_start_date', 'type': 'DATE', 'mode': 'NULLABLE'},
                {'name': 'current_period_end_date', 'type': 'DATE', 'mode': 'NULLABLE'},
                {'name': 'current_fiscal_year_start_date', 'type': 'DATE', 'mode': 'NULLABLE'},
                {'name': 'current_fiscal_year_end_date', 'type': 'DATE', 'mode': 'NULLABLE'},
                {'name': 'next_fiscal_year_start_date', 'type': 'DATE', 'mode': 'NULLABLE'},
                {'name': 'next_fiscal_year_end_date', 'type': 'DATE', 'mode': 'NULLABLE'},
                {'name': 'net_sales', 'type': 'INTEGER', 'mode': 'NULLABLE'},
                {'name': 'operating_profit', 'type': 'INTEGER', 'mode': 'NULLABLE'},
                {'name': 'ordinary_profit', 'type': 'INTEGER', 'mode': 'NULLABLE'},
                {'name': 'profit', 'type': 'INTEGER', 'mode': 'NULLABLE'},
                {'name': 'earnings_per_share', 'type': 'FLOAT', 'mode': 'NULLABLE'},
                {'name': 'diluted_earnings_per_share', 'type': 'FLOAT', 'mode': 'NULLABLE'},
                {'name': 'total_assets', 'type': 'INTEGER', 'mode': 'NULLABLE'},
                {'name': 'equity', 'type': 'INTEGER', 'mode': 'NULLABLE'},
                {'name': 'equity_to_asset_ratio', 'type': 'FLOAT', 'mode': 'NULLABLE'},
                {'name': 'book_value_per_share', 'type': 'FLOAT', 'mode': 'NULLABLE'},
                {'name': 'cash_flows_from_operating_activities', 'type': 'INTEGER', 'mode': 'NULLABLE'},
                {'name': 'cash_flows_from_investing_activities', 'type': 'INTEGER', 'mode': 'NULLABLE'},
                {'name': 'cash_flows_from_financing_activities', 'type': 'INTEGER', 'mode': 'NULLABLE'},
                {'name': 'cash_and_equivalents', 'type': 'INTEGER', 'mode': 'NULLABLE'},
            ],

            # 4. Dividend Information
            'dividends': [
                {'name': 'date', 'type': 'DATE', 'mode': 'REQUIRED'},
                {'name': 'announcement_date', 'type': 'DATE', 'mode': 'NULLABLE'},
                {'name': 'code', 'type': 'STRING', 'mode': 'REQUIRED'},
                {'name': 'record_date', 'type': 'DATE', 'mode': 'NULLABLE'},
                {'name': 'dividend_pay_date', 'type': 'DATE', 'mode': 'NULLABLE'},
                {'name': 'cash_dividend', 'type': 'FLOAT', 'mode': 'NULLABLE'},
                {'name': 'stock_dividend', 'type': 'FLOAT', 'mode': 'NULLABLE'},
                {'name': 'other_dividend', 'type': 'FLOAT', 'mode': 'NULLABLE'},
                {'name': 'total_dividend', 'type': 'FLOAT', 'mode': 'NULLABLE'},
            ],

            # 5. Market Indices
            'indices': [
                {'name': 'date', 'type': 'DATE', 'mode': 'REQUIRED'},
                {'name': 'code', 'type': 'STRING', 'mode': 'REQUIRED'},
                {'name': 'open', 'type': 'FLOAT', 'mode': 'NULLABLE'},
                {'name': 'high', 'type': 'FLOAT', 'mode': 'NULLABLE'},
                {'name': 'low', 'type': 'FLOAT', 'mode': 'NULLABLE'},
                {'name': 'close', 'type': 'FLOAT', 'mode': 'NULLABLE'},
            ],
        }

        return schemas

    def setup_all_tables(self):
        """Set up all BigQuery tables with proper schemas."""
        logger.info("Setting up all BigQuery tables...")

        schemas = self.create_table_schemas()

        for table_name, schema in schemas.items():
            try:
                # Create table with partitioning and clustering
                partition_field = 'date'
                cluster_fields = ['code'] if table_name != 'companies' else ['code', 'market_code']

                self.bigquery_client.create_table(
                    table_name=table_name,
                    schema=schema,
                    partition_field=partition_field,
                    cluster_fields=cluster_fields
                )
                logger.info(f"✅ Table {table_name} ready")

            except Exception as e:
                logger.error(f"❌ Failed to create table {table_name}: {e}")

        logger.info("All tables setup completed")

    def ingest_companies(self):
        """Ingest listed companies data."""
        logger.info("🏢 Starting companies data ingestion...")

        try:
            # Get companies data
            companies_df = self.jquants_client.get_listed_info()
            logger.info(f"Retrieved {len(companies_df)} companies")

            # Validate data
            required_columns = ['Date', 'Code', 'CompanyName']
            missing_columns = [col for col in required_columns if col not in companies_df.columns]
            if missing_columns:
                raise ValueError(f"Missing required columns: {missing_columns}")

            # Transform data for BigQuery
            companies_df = companies_df.rename(columns={
                'Date': 'date',
                'Code': 'code',
                'CompanyName': 'company_name',
                'CompanyNameEnglish': 'company_name_english',
                'Sector17Code': 'sector17_code',
                'Sector17CodeName': 'sector17_code_name',
                'Sector33Code': 'sector33_code',
                'Sector33CodeName': 'sector33_code_name',
                'ScaleCategory': 'scale_category',
                'MarketCode': 'market_code',
                'MarketCodeName': 'market_code_name',
                'MarginCode': 'margin_code',
                'MarginCodeName': 'margin_code_name',
            })

            # Insert into BigQuery
            job_id = self.bigquery_client.insert_dataframe(
                table_name='companies',
                dataframe=companies_df,
                write_disposition='WRITE_TRUNCATE'  # Replace existing data
            )

            logger.info(f"✅ Companies data ingested successfully. Job ID: {job_id}")
            return len(companies_df)

        except Exception as e:
            logger.error(f"❌ Companies ingestion failed: {e}")
            raise

    def ingest_daily_prices(self, days_back: int = 5):
        """Ingest daily stock prices data for recent days.

        Args:
            days_back: Number of days back to fetch data for
        """
        logger.info(f"📈 Starting daily prices data ingestion (last {days_back} days)...")

        try:
            all_prices = []
            total_records = 0

            # Get data for each of the last few days
            for i in range(days_back):
                target_date = datetime.now() - timedelta(days=i)
                date_str = target_date.strftime('%Y%m%d')

                logger.info(f"Fetching daily prices for {date_str}")

                try:
                    # Get daily prices data for specific date
                    prices_df = self.jquants_client.get_prices_daily_quotes(
                        date_yyyymmdd=date_str
                    )

                    if not prices_df.empty:
                        logger.info(f"Retrieved {len(prices_df)} price records for {date_str}")
                        all_prices.append(prices_df)
                        total_records += len(prices_df)
                    else:
                        logger.info(f"No price data for {date_str} (likely weekend/holiday)")

                    # Rate limiting
                    time.sleep(self.request_delay)

                except Exception as e:
                    logger.warning(f"Failed to get prices for {date_str}: {e}")
                    continue

            if not all_prices:
                logger.warning("No daily prices data retrieved for any date")
                return 0

            # Combine all dataframes
            import pandas as pd
            combined_df = pd.concat(all_prices, ignore_index=True)
            logger.info(f"Combined {total_records} price records from {len(all_prices)} days")

            # Validate data
            required_columns = ['Date', 'Code']
            missing_columns = [col for col in required_columns if col not in combined_df.columns]
            if missing_columns:
                raise ValueError(f"Missing required columns: {missing_columns}")

            # Transform data for BigQuery
            combined_df = combined_df.rename(columns={
                'Date': 'date',
                'Code': 'code',
                'Open': 'open',
                'High': 'high',
                'Low': 'low',
                'Close': 'close',
                'Volume': 'volume',
                'TurnoverValue': 'turnover_value',
                'AdjustmentFactor': 'adjustment_factor',
                'AdjustmentOpen': 'adjustment_open',
                'AdjustmentHigh': 'adjustment_high',
                'AdjustmentLow': 'adjustment_low',
                'AdjustmentClose': 'adjustment_close',
                'AdjustmentVolume': 'adjustment_volume',
            })

            # Insert into BigQuery
            job_id = self.bigquery_client.insert_dataframe(
                table_name='daily_prices',
                dataframe=combined_df,
                write_disposition='WRITE_APPEND'  # Append new data
            )

            logger.info(f"✅ Daily prices data ingested successfully. Job ID: {job_id}")
            return len(combined_df)

        except Exception as e:
            logger.error(f"❌ Daily prices ingestion failed: {e}")
            raise

    def ingest_financial_statements(self, limit_companies: int = 100):
        """Ingest financial statements data for a limited number of companies.

        Args:
            limit_companies: Number of companies to fetch financial data for
        """
        logger.info(f"💰 Starting financial statements data ingestion (top {limit_companies} companies)...")

        try:
            # Get list of companies first
            companies_df = self.jquants_client.get_listed_info()

            # Take only the first N companies to avoid overwhelming the API
            top_companies = companies_df.head(limit_companies)
            logger.info(f"Processing financial statements for {len(top_companies)} companies")

            all_statements = []
            total_records = 0

            for idx, company in top_companies.iterrows():
                code = company['Code']
                logger.info(f"Fetching financial statements for {code} ({idx+1}/{len(top_companies)})")

                try:
                    # Get financial statements for this company
                    statements_df = self.jquants_client.get_fins_statements(code=code)

                    if not statements_df.empty:
                        logger.info(f"Retrieved {len(statements_df)} financial records for {code}")
                        all_statements.append(statements_df)
                        total_records += len(statements_df)
                    else:
                        logger.info(f"No financial data for {code}")

                    # Rate limiting - be more conservative for financial data
                    time.sleep(self.request_delay * 2)

                except Exception as e:
                    logger.warning(f"Failed to get financial statements for {code}: {e}")
                    continue

                # Break every 20 companies to avoid overwhelming
                if (idx + 1) % 20 == 0:
                    logger.info(f"Processed {idx + 1} companies, taking a longer break...")
                    time.sleep(self.batch_delay)

            if not all_statements:
                logger.warning("No financial statements data retrieved")
                return 0

            # Combine all dataframes
            import pandas as pd
            combined_df = pd.concat(all_statements, ignore_index=True)
            logger.info(f"Combined {total_records} financial records from {len(all_statements)} companies")

            # Validate data
            required_columns = ['DisclosedDate', 'LocalCode']
            missing_columns = [col for col in required_columns if col not in combined_df.columns]
            if missing_columns:
                raise ValueError(f"Missing required columns: {missing_columns}")

            # Transform data for BigQuery
            # Add a date field for partitioning
            combined_df['date'] = combined_df['DisclosedDate']

            # Convert numeric columns to proper types
            numeric_columns = [
                'NetSales', 'OperatingProfit', 'OrdinaryProfit', 'Profit',
                'TotalAssets', 'Equity', 'CashFlowsFromOperatingActivities',
                'CashFlowsFromInvestingActivities', 'CashFlowsFromFinancingActivities',
                'CashAndEquivalents'
            ]

            for col in numeric_columns:
                if col in combined_df.columns:
                    # Convert to numeric, replacing empty strings and non-numeric values with NaN
                    combined_df[col] = pd.to_numeric(combined_df[col], errors='coerce')

            # Convert float columns to proper types
            float_columns = [
                'EarningsPerShare', 'DilutedEarningsPerShare', 'EquityToAssetRatio',
                'BookValuePerShare'
            ]

            for col in float_columns:
                if col in combined_df.columns:
                    combined_df[col] = pd.to_numeric(combined_df[col], errors='coerce')

            # Clean column names for BigQuery compatibility
            def clean_column_name(name):
                """Clean column name for BigQuery compatibility."""
                # Replace invalid characters with underscores
                import re
                cleaned = re.sub(r'[^a-zA-Z0-9_]', '_', name)
                # Remove consecutive underscores
                cleaned = re.sub(r'_+', '_', cleaned)
                # Remove leading/trailing underscores
                cleaned = cleaned.strip('_')
                # Ensure it starts with a letter or underscore
                if cleaned and not cleaned[0].isalpha() and cleaned[0] != '_':
                    cleaned = '_' + cleaned
                return cleaned.lower()

            # Clean all column names
            combined_df.columns = [clean_column_name(col) for col in combined_df.columns]

            # Now rename the cleaned columns to our standard names
            column_mapping = {}
            for old_col, new_col in [
                ('discloseddate', 'disclosure_date'),
                ('localcode', 'code'),
                ('disclosurenumber', 'disclosure_number'),
                ('typeofdocument', 'type_of_document'),
                ('typeofcurrentperiod', 'type_of_current_period'),
                ('currentperiodstartdate', 'current_period_start_date'),
                ('currentperiodenddate', 'current_period_end_date'),
                ('currentfiscalyearstartdate', 'current_fiscal_year_start_date'),
                ('currentfiscalyearenddate', 'current_fiscal_year_end_date'),
                ('nextfiscalyearstartdate', 'next_fiscal_year_start_date'),
                ('nextfiscalyearenddate', 'next_fiscal_year_end_date'),
                ('netsales', 'net_sales'),
                ('operatingprofit', 'operating_profit'),
                ('ordinaryprofit', 'ordinary_profit'),
                ('profit', 'profit'),
                ('earningspershare', 'earnings_per_share'),
                ('dilutedearningspershare', 'diluted_earnings_per_share'),
                ('totalassets', 'total_assets'),
                ('equity', 'equity'),
                ('equitytoassetratio', 'equity_to_asset_ratio'),
                ('bookvaluepershare', 'book_value_per_share'),
                ('cashflowsfromoperatingactivities', 'cash_flows_from_operating_activities'),
                ('cashflowsfrominvestingactivities', 'cash_flows_from_investing_activities'),
                ('cashflowsfromfinancingactivities', 'cash_flows_from_financing_activities'),
                ('cashandequivalents', 'cash_and_equivalents'),
            ]:
                if old_col in combined_df.columns:
                    column_mapping[old_col] = new_col

            combined_df = combined_df.rename(columns=column_mapping)

            # Select only the columns we want to keep (those in our schema)
            schema_columns = [
                'date', 'disclosure_date', 'code', 'disclosure_number', 'type_of_document',
                'type_of_current_period', 'current_period_start_date', 'current_period_end_date',
                'current_fiscal_year_start_date', 'current_fiscal_year_end_date',
                'next_fiscal_year_start_date', 'next_fiscal_year_end_date',
                'net_sales', 'operating_profit', 'ordinary_profit', 'profit',
                'earnings_per_share', 'diluted_earnings_per_share', 'total_assets', 'equity',
                'equity_to_asset_ratio', 'book_value_per_share',
                'cash_flows_from_operating_activities', 'cash_flows_from_investing_activities',
                'cash_flows_from_financing_activities', 'cash_and_equivalents'
            ]

            # Keep only columns that exist in both the dataframe and our schema
            available_columns = [col for col in schema_columns if col in combined_df.columns]
            combined_df = combined_df[available_columns]

            # Insert into BigQuery
            job_id = self.bigquery_client.insert_dataframe(
                table_name='financial_statements',
                dataframe=combined_df,
                write_disposition='WRITE_APPEND'  # Append new data
            )

            logger.info(f"✅ Financial statements data ingested successfully. Job ID: {job_id}")
            return len(combined_df)

        except Exception as e:
            logger.error(f"❌ Financial statements ingestion failed: {e}")
            raise

    def run_comprehensive_ingestion(self):
        """Run comprehensive data ingestion for all endpoints."""
        logger.info("🚀 Starting comprehensive J-Quants data ingestion...")

        start_time = datetime.now()
        results = {}

        try:
            # 1. Setup all tables
            self.setup_all_tables()
            time.sleep(self.batch_delay)

            # 2. Ingest companies (foundation data)
            logger.info("=" * 60)
            results['companies'] = self.ingest_companies()
            time.sleep(self.batch_delay)

            # 3. Ingest daily prices (last 5 days)
            logger.info("=" * 60)
            results['daily_prices'] = self.ingest_daily_prices(days_back=5)
            time.sleep(self.batch_delay)

            # 4. Ingest financial statements (limited to 20 companies for now)
            logger.info("=" * 60)
            results['financial_statements'] = self.ingest_financial_statements(limit_companies=20)
            time.sleep(self.batch_delay)

            # 5. More endpoints will be added here...
            logger.info("=" * 60)
            logger.info("📊 Phase 3 Complete - Companies, Daily Prices, and Financial Statements data ingested")
            logger.info("Next phases (dividends, indices, etc.) will be implemented step by step")

        except Exception as e:
            logger.error(f"❌ Comprehensive ingestion failed: {e}")
            raise

        finally:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            logger.info("=" * 60)
            logger.info("📈 COMPREHENSIVE INGESTION SUMMARY")
            logger.info("=" * 60)
            logger.info(f"Duration: {duration:.2f} seconds")

            for data_type, count in results.items():
                logger.info(f"{data_type.capitalize()}: {count:,} records")

            logger.info("=" * 60)

def main():
    """Main function."""
    pipeline = ComprehensiveJQuantsPipeline()
    pipeline.run_comprehensive_ingestion()

if __name__ == "__main__":
    main()
