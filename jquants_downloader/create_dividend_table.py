#!/usr/bin/env python3

from google.cloud import bigquery

# Initialize BigQuery client
client = bigquery.Client(project='tokyotickers')

# Define the schema for the dividends table
schema = [
    bigquery.SchemaField("AnnouncementDate", "DATETIME"),
    bigquery.SchemaField("AnnouncementTime", "STRING"),
    bigquery.SchemaField("Code", "STRING"),
    bigquery.SchemaField("ReferenceNumber", "STRING"),
    bigquery.SchemaField("StatusCode", "STRING"),
    bigquery.SchemaField("BoardMeetingDate", "STRING"),
    bigquery.SchemaField("InterimFinalCode", "STRING"),
    bigquery.SchemaField("ForecastResultCode", "STRING"),
    bigquery.SchemaField("InterimFinalTerm", "STRING"),
    bigquery.<PERSON>hemaField("GrossDividendRate", "STRING"),
    bigquery.<PERSON><PERSON><PERSON><PERSON><PERSON>("RecordDate", "STRING"),
    bigquery.<PERSON><PERSON>a<PERSON>ield("ExDate", "STRING"),
    bigquery.<PERSON>hemaField("ActualRecordDate", "STRING"),
    bigquery.SchemaField("PayableDate", "STRING"),
    bigquery.SchemaField("CAReferenceNumber", "STRING"),
    bigquery.SchemaField("DistributionAmount", "STRING"),
    bigquery.SchemaField("RetainedEarnings", "STRING"),
    bigquery.SchemaField("DeemedDividend", "STRING"),
    bigquery.SchemaField("DeemedCapitalGains", "STRING"),
    bigquery.SchemaField("NetAssetDecreaseRatio", "STRING"),
    bigquery.SchemaField("CommemorativeSpecialCode", "STRING"),
    bigquery.SchemaField("CommemorativeDividendRate", "STRING"),
    bigquery.SchemaField("SpecialDividendRate", "STRING"),
]

# Create the table
table_id = "tokyotickers.jquants_data.dividends"
table = bigquery.Table(table_id, schema=schema)

try:
    # Delete existing table if it exists
    client.delete_table(table_id, not_found_ok=True)
    print(f"Deleted existing table {table_id}")
    
    # Create new table
    table = client.create_table(table)
    print(f"Created table {table.project}.{table.dataset_id}.{table.table_id}")
    
except Exception as e:
    print(f"Error: {e}")
