"""Data models for J-Quants API responses."""

from datetime import date as date_type
from typing import Optional, List
from pydantic import BaseModel, Field


class ListedCompany(BaseModel):
    """Model for listed company information from J-Quants API."""

    date: date_type = Field(description="Date of application of information", alias="Date")
    code: str = Field(description="Issue code", alias="Code")
    company_name: str = Field(description="Company Name (Japanese)", alias="CompanyName")
    company_name_english: Optional[str] = Field(default=None, description="Company Name (English)", alias="CompanyNameEnglish")
    sector17_code: Optional[str] = Field(default=None, description="17-Sector code", alias="Sector17Code")
    sector17_code_name: Optional[str] = Field(default=None, description="17-Sector code name (Japanese)", alias="Sector17CodeName")
    sector33_code: Optional[str] = Field(default=None, description="33-Sector code", alias="Sector33Code")
    sector33_code_name: Optional[str] = Field(default=None, description="33-Sector code name (Japanese)", alias="Sector33CodeName")
    scale_category: Optional[str] = Field(default=None, description="TOPIX Scale category", alias="ScaleCategory")
    market_code: Optional[str] = Field(default=None, description="Market segment code", alias="MarketCode")
    market_code_name: Optional[str] = Field(default=None, description="Market segment code name (Japanese)", alias="MarketCodeName")
    margin_code: Optional[str] = Field(default=None, description="Flags of margin and loan issues", alias="MarginCode")
    margin_code_name: Optional[str] = Field(default=None, description="Name of flags of margin and loan issues", alias="MarginCodeName")

    model_config = {
        "populate_by_name": True,
        "str_strip_whitespace": True
    }


class ListedCompaniesResponse(BaseModel):
    """Model for J-Quants listed companies API response."""

    info: List[ListedCompany] = Field(description="List of company information")
    pagination_key: Optional[str] = Field(default=None, description="Pagination key for next page")

    model_config = {
        "populate_by_name": True
    }


class AuthTokenResponse(BaseModel):
    """Model for J-Quants authentication token response."""

    refresh_token: Optional[str] = Field(default=None, alias="refreshToken")
    id_token: Optional[str] = Field(default=None, alias="idToken")

    model_config = {
        "populate_by_name": True
    }


class APIError(BaseModel):
    """Model for J-Quants API error response."""

    message: str = Field(description="Error message")

    model_config = {
        "populate_by_name": True
    }
