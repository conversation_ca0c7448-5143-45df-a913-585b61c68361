"""Data models for J-Quants API responses."""

from datetime import date as date_type
from typing import Optional, List, Union
from pydantic import BaseModel, Field


class ListedCompany(BaseModel):
    """Model for listed company information from J-Quants API."""

    date: date_type = Field(description="Date of application of information", alias="Date")
    code: str = Field(description="Issue code", alias="Code")
    company_name: str = Field(description="Company Name (Japanese)", alias="CompanyName")
    company_name_english: Optional[str] = Field(default=None, description="Company Name (English)", alias="CompanyNameEnglish")
    sector17_code: Optional[str] = Field(default=None, description="17-Sector code", alias="Sector17Code")
    sector17_code_name: Optional[str] = Field(default=None, description="17-Sector code name (Japanese)", alias="Sector17CodeName")
    sector33_code: Optional[str] = Field(default=None, description="33-Sector code", alias="Sector33Code")
    sector33_code_name: Optional[str] = Field(default=None, description="33-Sector code name (Japanese)", alias="Sector33CodeName")
    scale_category: Optional[str] = Field(default=None, description="TOPIX Scale category", alias="ScaleCategory")
    market_code: Optional[str] = Field(default=None, description="Market segment code", alias="MarketCode")
    market_code_name: Optional[str] = Field(default=None, description="Market segment code name (Japanese)", alias="MarketCodeName")
    margin_code: Optional[str] = Field(default=None, description="Flags of margin and loan issues", alias="MarginCode")
    margin_code_name: Optional[str] = Field(default=None, description="Name of flags of margin and loan issues", alias="MarginCodeName")

    model_config = {
        "populate_by_name": True,
        "str_strip_whitespace": True
    }


class DailyPrice(BaseModel):
    """Model for daily stock price data (OHLC)."""
    
    date: date_type = Field(description="Trading date")
    code: str = Field(description="Issue code")
    open: Optional[float] = Field(default=None, description="Open price (before adjustment)")
    high: Optional[float] = Field(default=None, description="High price (before adjustment)")
    low: Optional[float] = Field(default=None, description="Low price (before adjustment)")
    close: Optional[float] = Field(default=None, description="Close price (before adjustment)")
    upper_limit: str = Field(default="0", description="Flag of hitting upper price limit")
    lower_limit: str = Field(default="0", description="Flag of hitting lower price limit")
    volume: Optional[float] = Field(default=None, description="Trading volume (before adjustment)")
    turnover_value: Optional[float] = Field(default=None, description="Trading value")
    adjustment_factor: float = Field(default=1.0, description="Adjustment factor")
    adjustment_open: Optional[float] = Field(default=None, description="Adjusted open price")
    adjustment_high: Optional[float] = Field(default=None, description="Adjusted high price")
    adjustment_low: Optional[float] = Field(default=None, description="Adjusted low price")
    adjustment_close: Optional[float] = Field(default=None, description="Adjusted close price")
    adjustment_volume: Optional[float] = Field(default=None, description="Adjusted trading volume")
    
    # Premium plan fields - morning session
    morning_open: Optional[float] = Field(default=None, description="Morning session open price")
    morning_high: Optional[float] = Field(default=None, description="Morning session high price")
    morning_low: Optional[float] = Field(default=None, description="Morning session low price")
    morning_close: Optional[float] = Field(default=None, description="Morning session close price")
    morning_volume: Optional[float] = Field(default=None, description="Morning session volume")
    morning_turnover_value: Optional[float] = Field(default=None, description="Morning session turnover")
    
    # Premium plan fields - afternoon session
    afternoon_open: Optional[float] = Field(default=None, description="Afternoon session open price")
    afternoon_high: Optional[float] = Field(default=None, description="Afternoon session high price")
    afternoon_low: Optional[float] = Field(default=None, description="Afternoon session low price")
    afternoon_close: Optional[float] = Field(default=None, description="Afternoon session close price")
    afternoon_volume: Optional[float] = Field(default=None, description="Afternoon session volume")
    afternoon_turnover_value: Optional[float] = Field(default=None, description="Afternoon session turnover")

    model_config = {
        "populate_by_name": True,
        "str_strip_whitespace": True
    }


class FinancialStatement(BaseModel):
    """Model for financial statement data."""
    
    disclosed_date: Optional[date_type] = Field(default=None, description="Disclosed date")
    disclosed_time: Optional[str] = Field(default=None, description="Disclosed time")
    local_code: str = Field(description="Issue code (5-character)")
    disclosure_number: Optional[str] = Field(default=None, description="Disclosure number")
    type_of_document: Optional[str] = Field(default=None, description="Type of document")
    type_of_current_period: Optional[str] = Field(default=None, description="Type of current period (1Q, 2Q, 3Q, 4Q, FY)")
    current_period_start_date: Optional[date_type] = Field(default=None, description="Current period start date")
    current_period_end_date: Optional[date_type] = Field(default=None, description="Current period end date")
    current_fiscal_year_start_date: Optional[date_type] = Field(default=None, description="Current fiscal year start date")
    current_fiscal_year_end_date: Optional[date_type] = Field(default=None, description="Current fiscal year end date")
    next_fiscal_year_start_date: Optional[date_type] = Field(default=None, description="Next fiscal year start date")
    next_fiscal_year_end_date: Optional[date_type] = Field(default=None, description="Next fiscal year end date")
    net_sales: Optional[str] = Field(default=None, description="Net sales")
    operating_profit: Optional[str] = Field(default=None, description="Operating profit")
    ordinary_profit: Optional[str] = Field(default=None, description="Ordinary profit")
    profit: Optional[str] = Field(default=None, description="Profit")
    earnings_per_share: Optional[str] = Field(default=None, description="Earnings per share")
    diluted_earnings_per_share: Optional[str] = Field(default=None, description="Diluted earnings per share")
    total_assets: Optional[str] = Field(default=None, description="Total assets")
    equity: Optional[str] = Field(default=None, description="Equity")
    equity_to_asset_ratio: Optional[str] = Field(default=None, description="Equity to asset ratio")
    book_value_per_share: Optional[str] = Field(default=None, description="Book value per share")
    cash_flows_from_operating_activities: Optional[str] = Field(default=None, description="Cash flows from operating activities")
    cash_flows_from_investing_activities: Optional[str] = Field(default=None, description="Cash flows from investing activities")
    cash_flows_from_financing_activities: Optional[str] = Field(default=None, description="Cash flows from financing activities")
    cash_and_equivalents: Optional[str] = Field(default=None, description="Cash and equivalents")

    model_config = {
        "populate_by_name": True,
        "str_strip_whitespace": True
    }


class Dividend(BaseModel):
    """Model for dividend information."""
    
    announcement_date: Optional[date_type] = Field(default=None, description="Announcement date")
    announcement_time: Optional[str] = Field(default=None, description="Announcement time")
    code: str = Field(description="Issue code")
    reference_number: Optional[str] = Field(default=None, description="Reference number")
    status_code: Optional[str] = Field(default=None, description="Status code (1=new, 2=revised, 3=delete)")
    board_meeting_date: Optional[date_type] = Field(default=None, description="Board meeting date")
    interim_final_code: Optional[str] = Field(default=None, description="Interim/final code (1=interim, 2=final)")
    forecast_result_code: Optional[str] = Field(default=None, description="Forecast/result code (1=result, 2=forecast)")
    interim_final_term: Optional[str] = Field(default=None, description="Interim final term")
    gross_dividend_rate: Optional[str] = Field(default=None, description="Dividend value per share")
    record_date: Optional[date_type] = Field(default=None, description="Record date")
    ex_date: Optional[date_type] = Field(default=None, description="Ex-rights date")
    actual_record_date: Optional[date_type] = Field(default=None, description="Date of dividend vesting")
    payable_date: Optional[date_type] = Field(default=None, description="Scheduled payment start date")
    ca_reference_number: Optional[str] = Field(default=None, description="CA reference number")
    distribution_amount: Optional[str] = Field(default=None, description="Amount of cash delivered per share")
    retained_earnings: Optional[str] = Field(default=None, description="Retained earnings per share")
    deemed_dividend: Optional[str] = Field(default=None, description="Deemed dividend per share")
    deemed_capital_gains: Optional[str] = Field(default=None, description="Amount of deemed transfer income per share")
    net_asset_decrease_ratio: Optional[str] = Field(default=None, description="Decrease ratio in net assets")
    commemorative_special_code: Optional[str] = Field(default=None, description="Commemorative/special code (0=Normal, 1=Commemorative, 2=Special, 3=Both)")
    commemorative_dividend_rate: Optional[str] = Field(default=None, description="Commemorative dividend value per share")
    special_dividend_rate: Optional[str] = Field(default=None, description="Special dividend value per share")

    model_config = {
        "populate_by_name": True,
        "str_strip_whitespace": True
    }


class TopixData(BaseModel):
    """Model for TOPIX index data."""
    
    date: date_type = Field(description="Trading date")
    open: float = Field(description="Open price")
    high: float = Field(description="High price")
    low: float = Field(description="Low price")
    close: float = Field(description="Close price")

    model_config = {
        "populate_by_name": True,
        "str_strip_whitespace": True
    }


class TradingByType(BaseModel):
    """Model for trading data by investor type."""
    
    date: date_type = Field(description="Trading date")
    section: str = Field(description="Market section")
    proprietary_total: Optional[float] = Field(default=None, description="Proprietary trading total")
    individual_total: Optional[float] = Field(default=None, description="Individual investor total")
    foreigner_total: Optional[float] = Field(default=None, description="Foreign investor total")
    securities_companies_total: Optional[float] = Field(default=None, description="Securities companies total")
    investment_trusts_total: Optional[float] = Field(default=None, description="Investment trusts total")
    business_companies_total: Optional[float] = Field(default=None, description="Business companies total")
    other_corporations_total: Optional[float] = Field(default=None, description="Other corporations total")
    insurance_companies_total: Optional[float] = Field(default=None, description="Insurance companies total")
    city_banks_regional_banks_etc_total: Optional[float] = Field(default=None, description="Banks total")
    trust_banks_total: Optional[float] = Field(default=None, description="Trust banks total")
    other_financial_institutions_total: Optional[float] = Field(default=None, description="Other financial institutions total")
    pension_funds_total: Optional[float] = Field(default=None, description="Pension funds total")

    model_config = {
        "populate_by_name": True,
        "str_strip_whitespace": True
    }


class MarginTrading(BaseModel):
    """Model for margin trading breakdown data."""
    
    date: date_type = Field(description="Trading date")
    code: str = Field(description="Issue code")
    long_sell_value: Optional[float] = Field(default=None, description="Long selling trading value")
    short_sell_without_margin_value: Optional[float] = Field(default=None, description="Short selling trading value (excluding new margin sell)")
    margin_sell_new_value: Optional[float] = Field(default=None, description="New margin selling trading value")
    margin_sell_close_value: Optional[float] = Field(default=None, description="Closing margin selling trading value")
    long_buy_value: Optional[float] = Field(default=None, description="Long buying trading value")
    margin_buy_new_value: Optional[float] = Field(default=None, description="New margin buying trading value")
    margin_buy_close_value: Optional[float] = Field(default=None, description="Closing margin buying trading value")
    long_sell_volume: Optional[float] = Field(default=None, description="Long selling trading volume")
    short_sell_without_margin_volume: Optional[float] = Field(default=None, description="Short selling trading volume (excluding new margin sell)")
    margin_sell_new_volume: Optional[float] = Field(default=None, description="New margin selling trading volume")
    margin_sell_close_volume: Optional[float] = Field(default=None, description="Closing margin selling trading volume")
    long_buy_volume: Optional[float] = Field(default=None, description="Long buying trading volume")
    margin_buy_new_volume: Optional[float] = Field(default=None, description="New margin buying trading volume")
    margin_buy_close_volume: Optional[float] = Field(default=None, description="Closing margin buying trading volume")

    model_config = {
        "populate_by_name": True,
        "str_strip_whitespace": True
    }


class ListedCompaniesResponse(BaseModel):
    """Model for J-Quants listed companies API response."""

    info: List[ListedCompany] = Field(description="List of company information")
    pagination_key: Optional[str] = Field(default=None, description="Pagination key for next page")

    model_config = {
        "populate_by_name": True
    }


class AuthTokenResponse(BaseModel):
    """Model for J-Quants authentication token response."""

    refresh_token: Optional[str] = Field(default=None, alias="refreshToken")
    id_token: Optional[str] = Field(default=None, alias="idToken")

    model_config = {
        "populate_by_name": True
    }


class APIError(BaseModel):
    """Model for J-Quants API error response."""

    message: str = Field(description="Error message")

    model_config = {
        "populate_by_name": True
    }