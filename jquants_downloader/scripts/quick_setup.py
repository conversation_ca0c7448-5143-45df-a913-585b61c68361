#!/usr/bin/env python3
"""Quick setup script for J-Quants pipeline with multiple environment support."""

import os
import sys
import subprocess
from pathlib import Path


def run_command(command, check=True):
    """Run a command and return the result."""
    print(f"Running: {command}")
    try:
        result = subprocess.run(command, shell=True, check=check, capture_output=True, text=True)
        if result.stdout:
            print(result.stdout.strip())
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        print(f"Error: {e}")
        if e.stderr:
            print(f"Error output: {e.stderr.strip()}")
        return False


def main():
    """Main setup function."""
    print("J-Quants Pipeline Quick Setup")
    print("=" * 40)
    
    # Check if we're in the right directory
    if not Path("requirements.txt").exists():
        print("Error: Please run this script from the jquants_downloader directory")
        sys.exit(1)
    
    # Step 1: Install dependencies
    print("\n1. Installing dependencies...")
    if not run_command("pip install -r requirements.txt"):
        print("Failed to install dependencies")
        sys.exit(1)
    
    # Step 2: Ask for project setup
    print("\n2. Project Setup")
    print("Do you want to:")
    print("  1. Use the default tokyo_tickers project")
    print("  2. Create a new project")
    print("  3. Use an existing project")
    
    choice = input("Enter choice (1-3): ").strip()
    
    if choice == "1":
        project_id = "tokyo_tickers"
        env_name = "tokyo_tickers"
        setup_new_project = True
    elif choice == "2":
        project_id = input("Enter new project ID: ").strip()
        env_name = input("Enter environment name (e.g., production, development): ").strip()
        setup_new_project = True
    elif choice == "3":
        project_id = input("Enter existing project ID: ").strip()
        env_name = input("Enter environment name: ").strip()
        setup_new_project = False
    else:
        print("Invalid choice")
        sys.exit(1)
    
    # Step 3: Create environment
    print(f"\n3. Creating environment '{env_name}' for project '{project_id}'...")
    if not run_command(f"python env_manager.py create {env_name} {project_id} --region asia-northeast1"):
        print("Failed to create environment")
        sys.exit(1)
    
    # Step 4: Switch to environment
    print(f"\n4. Switching to environment '{env_name}'...")
    if not run_command(f"python env_manager.py switch {env_name}"):
        print("Failed to switch environment")
        sys.exit(1)
    
    # Step 5: Setup GCP project (if new)
    if setup_new_project:
        print(f"\n5. Setting up GCP project '{project_id}'...")
        setup_cmd = f"python setup_gcp.py --project-id {project_id} --region asia-northeast1"
        
        billing_account = input("Enter billing account ID (optional, press Enter to skip): ").strip()
        if billing_account:
            setup_cmd += f" --billing-account {billing_account}"
        
        print("This will create the GCP project and set up all required services.")
        confirm = input("Continue? (y/N): ").strip().lower()
        
        if confirm == 'y':
            if not run_command(setup_cmd):
                print("GCP setup failed. You may need to set up the project manually.")
        else:
            print("Skipping GCP setup. You'll need to set up the project manually.")
    
    # Step 6: Configure credentials
    print(f"\n6. Configuration")
    print("Please update the .env file with your credentials:")
    print("  - JQUANTS_EMAIL: Your J-Quants account email")
    print("  - JQUANTS_PASSWORD: Your J-Quants account password")
    print("  - GOOGLE_APPLICATION_CREDENTIALS: Path to your service account key file")
    
    # Step 7: Test setup
    print(f"\n7. Testing setup...")
    if not run_command("python test_setup.py"):
        print("Setup test failed. Please check your configuration.")
        sys.exit(1)
    
    # Step 8: Setup BigQuery
    print(f"\n8. Setting up BigQuery infrastructure...")
    if not run_command("python main.py --setup-only"):
        print("BigQuery setup failed. Please check your credentials and try again.")
        sys.exit(1)
    
    # Success!
    print("\n" + "=" * 50)
    print("🎉 SETUP COMPLETED SUCCESSFULLY!")
    print("=" * 50)
    print(f"Environment: {env_name}")
    print(f"Project: {project_id}")
    print(f"Region: asia-northeast1 (Tokyo)")
    
    print("\nNext steps:")
    print("1. Verify your J-Quants credentials in .env")
    print("2. Run: python main.py")
    print("3. Check your data in BigQuery console")
    
    print("\nUseful commands:")
    print("  make env-current    # Show current environment")
    print("  make run           # Run data pipeline")
    print("  make validate      # Validate data quality")
    print("  make env-list      # List all environments")


if __name__ == "__main__":
    main()
