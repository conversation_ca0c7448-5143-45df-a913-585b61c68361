#!/bin/bash
# Apply rate limit fixes to production service

echo "🔧 Applying rate limit fixes to production_ingestion_service.py"
echo "============================================================"

# Create a backup
cp production_ingestion_service.py production_ingestion_service.py.backup

# Apply the fixes using sed
echo "1️⃣ Updating request_delay from 10.0 to 60.0 seconds..."
sed -i 's/self.request_delay = 10.0/self.request_delay = 60.0/' production_ingestion_service.py

echo "2️⃣ Updating batch_delay from 30.0 to 300.0 seconds..."
sed -i 's/self.batch_delay = 30.0/self.batch_delay = 300.0/' production_ingestion_service.py

echo "3️⃣ Checking changes..."
echo ""
echo "Updated settings:"
grep -n "request_delay\|batch_delay" production_ingestion_service.py | head -5

echo ""
echo "✅ Basic rate limit fixes applied!"
echo ""
echo "⚠️  IMPORTANT: You still need to manually add 429 error handling"
echo "   See update_production_for_rate_limits.py for details"
echo ""
echo "📋 Next steps:"
echo "1. Wait 2-6 hours for rate limits to reset"
echo "2. Deploy updated service: make deploy"
echo "3. Or use emergency script: make emergency"