"""BigQuery client for data storage and management."""

import logging
from typing import List, Optional, Dict, Any
from google.cloud import bigquery
from google.cloud.exceptions import NotFound
import pandas as pd

from config import Config
from models import ListedCompany

logger = logging.getLogger(__name__)


class BigQueryClient:
    """Client for interacting with Google BigQuery."""

    def __init__(self, project_id: str, dataset_id: str, location: str = "US"):
        """Initialize BigQuery client.

        Args:
            project_id: Google Cloud project ID
            dataset_id: BigQuery dataset ID
            location: BigQuery location/region
        """
        self.project_id = project_id
        self.dataset_id = dataset_id
        self.location = location
        self.client = bigquery.Client(project=project_id)
        self.dataset_ref = self.client.dataset(dataset_id)

        logger.info(f"Initialized BigQuery client for project: {project_id}, dataset: {dataset_id}")

    def create_dataset(self) -> None:
        """Create BigQuery dataset if it doesn't exist."""
        try:
            self.client.get_dataset(self.dataset_ref)
            logger.info(f"Dataset {self.dataset_id} already exists")
        except NotFound:
            logger.info(f"Creating dataset {self.dataset_id}")
            dataset = bigquery.Dataset(self.dataset_ref)
            dataset.location = self.location
            dataset.description = "J-Quants financial data"

            dataset = self.client.create_dataset(dataset, timeout=30)
            logger.info(f"Created dataset {dataset.dataset_id}")

    def create_companies_table(self) -> None:
        """Create companies table with appropriate schema."""
        table_id = f"{self.project_id}.{self.dataset_id}.{Config.COMPANIES_TABLE}"

        try:
            self.client.get_table(table_id)
            logger.info(f"Table {Config.COMPANIES_TABLE} already exists")
            return
        except NotFound:
            pass

        logger.info(f"Creating table {Config.COMPANIES_TABLE}")

        schema = [
            bigquery.SchemaField("date", "DATE", mode="REQUIRED", description="Date of application of information"),
            bigquery.SchemaField("code", "STRING", mode="REQUIRED", description="Issue code"),
            bigquery.SchemaField("company_name", "STRING", mode="REQUIRED", description="Company Name (Japanese)"),
            bigquery.SchemaField("company_name_english", "STRING", mode="NULLABLE", description="Company Name (English)"),
            bigquery.SchemaField("sector17_code", "STRING", mode="NULLABLE", description="17-Sector code"),
            bigquery.SchemaField("sector17_code_name", "STRING", mode="NULLABLE", description="17-Sector code name (Japanese)"),
            bigquery.SchemaField("sector33_code", "STRING", mode="NULLABLE", description="33-Sector code"),
            bigquery.SchemaField("sector33_code_name", "STRING", mode="NULLABLE", description="33-Sector code name (Japanese)"),
            bigquery.SchemaField("scale_category", "STRING", mode="NULLABLE", description="TOPIX Scale category"),
            bigquery.SchemaField("market_code", "STRING", mode="NULLABLE", description="Market segment code"),
            bigquery.SchemaField("market_code_name", "STRING", mode="NULLABLE", description="Market segment code name (Japanese)"),
            bigquery.SchemaField("margin_code", "STRING", mode="NULLABLE", description="Flags of margin and loan issues"),
            bigquery.SchemaField("margin_code_name", "STRING", mode="NULLABLE", description="Name of flags of margin and loan issues"),
            bigquery.SchemaField("ingestion_timestamp", "TIMESTAMP", mode="REQUIRED", description="When the data was ingested"),
        ]

        table = bigquery.Table(table_id, schema=schema)

        # Set up partitioning by date for better performance
        table.time_partitioning = bigquery.TimePartitioning(
            type_=bigquery.TimePartitioningType.DAY,
            field="date"
        )

        # Set up clustering for better query performance
        table.clustering_fields = ["code", "market_code"]

        table = self.client.create_table(table, timeout=30)
        logger.info(f"Created table {table.table_id}")

    def insert_companies_data(self, companies: List[ListedCompany], write_disposition: str = "WRITE_APPEND") -> None:
        """Insert companies data into BigQuery.

        Args:
            companies: List of ListedCompany objects
            write_disposition: How to handle existing data (WRITE_APPEND, WRITE_TRUNCATE, WRITE_EMPTY)
        """
        if not companies:
            logger.warning("No companies data to insert")
            return

        table_id = f"{self.project_id}.{self.dataset_id}.{Config.COMPANIES_TABLE}"
        logger.info(f"Inserting {len(companies)} companies into {table_id}")

        # Convert to DataFrame for easier handling
        data = []
        for company in companies:
            row = {
                "date": company.date,
                "code": company.code,
                "company_name": company.company_name,
                "company_name_english": company.company_name_english,
                "sector17_code": company.sector17_code,
                "sector17_code_name": company.sector17_code_name,
                "sector33_code": company.sector33_code,
                "sector33_code_name": company.sector33_code_name,
                "scale_category": company.scale_category,
                "market_code": company.market_code,
                "market_code_name": company.market_code_name,
                "margin_code": company.margin_code,
                "margin_code_name": company.margin_code_name,
                "ingestion_timestamp": pd.Timestamp.now(tz="UTC"),
            }
            data.append(row)

        df = pd.DataFrame(data)

        # Configure load job
        job_config = bigquery.LoadJobConfig(
            write_disposition=write_disposition,
            schema_update_options=[bigquery.SchemaUpdateOption.ALLOW_FIELD_ADDITION],
        )

        # Load data
        job = self.client.load_table_from_dataframe(df, table_id, job_config=job_config)
        job.result()  # Wait for job to complete

        logger.info(f"Successfully inserted {len(companies)} companies. Job ID: {job.job_id}")

    def get_companies_count(self) -> int:
        """Get total count of companies in the table.

        Returns:
            Number of companies in the table
        """
        table_id = f"{self.project_id}.{self.dataset_id}.{Config.COMPANIES_TABLE}"

        query = f"SELECT COUNT(*) as count FROM `{table_id}`"

        try:
            query_job = self.client.query(query)
            results = query_job.result()

            for row in results:
                return row.count

        except Exception as e:
            logger.error(f"Error getting companies count: {e}")
            return 0

        return 0

    def get_latest_companies_by_date(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get latest companies data by date.

        Args:
            limit: Maximum number of records to return

        Returns:
            List of company records
        """
        table_id = f"{self.project_id}.{self.dataset_id}.{Config.COMPANIES_TABLE}"

        query = f"""
        SELECT *
        FROM `{table_id}`
        ORDER BY date DESC, ingestion_timestamp DESC
        LIMIT {limit}
        """

        try:
            query_job = self.client.query(query)
            results = query_job.result()

            companies = []
            for row in results:
                companies.append(dict(row))

            return companies

        except Exception as e:
            logger.error(f"Error getting latest companies: {e}")
            return []

    def delete_companies_by_date(self, date: str) -> int:
        """Delete companies data for a specific date.

        Args:
            date: Date in YYYY-MM-DD format

        Returns:
            Number of rows deleted
        """
        table_id = f"{self.project_id}.{self.dataset_id}.{Config.COMPANIES_TABLE}"

        query = f"""
        DELETE FROM `{table_id}`
        WHERE date = '{date}'
        """

        try:
            query_job = self.client.query(query)
            query_job.result()  # Wait for job to complete

            logger.info(f"Deleted companies data for date: {date}")
            return query_job.num_dml_affected_rows or 0

        except Exception as e:
            logger.error(f"Error deleting companies data: {e}")
            return 0

    def create_table(self, table_name: str, schema: List[Dict[str, Any]],
                     partition_field: Optional[str] = None,
                     cluster_fields: Optional[List[str]] = None) -> None:
        """Create a BigQuery table with the given schema.

        Args:
            table_name: Name of the table to create
            schema: List of field definitions
            partition_field: Field to partition by (optional)
            cluster_fields: Fields to cluster by (optional)
        """
        table_id = f"{self.project_id}.{self.dataset_id}.{table_name}"

        try:
            self.client.get_table(table_id)
            logger.info(f"Table {table_name} already exists")
            return
        except NotFound:
            pass

        logger.info(f"Creating table {table_name}")

        # Convert schema to BigQuery schema fields
        bq_schema = []
        for field in schema:
            bq_schema.append(bigquery.SchemaField(
                name=field['name'],
                field_type=field['type'],
                mode=field.get('mode', 'NULLABLE'),
                description=field.get('description', '')
            ))

        table = bigquery.Table(table_id, schema=bq_schema)

        # Set up partitioning if specified
        if partition_field:
            table.time_partitioning = bigquery.TimePartitioning(
                type_=bigquery.TimePartitioningType.DAY,
                field=partition_field
            )

        # Set up clustering if specified
        if cluster_fields:
            table.clustering_fields = cluster_fields

        table = self.client.create_table(table, timeout=30)
        logger.info(f"Created table {table.table_id}")

    def insert_dataframe(self, table_name: str, dataframe: pd.DataFrame,
                        write_disposition: str = "WRITE_APPEND") -> str:
        """Insert a pandas DataFrame into a BigQuery table.

        Args:
            table_name: Name of the target table
            dataframe: DataFrame to insert
            write_disposition: How to handle existing data

        Returns:
            Job ID of the load operation
        """
        # Handle case where API returns string instead of DataFrame
        if isinstance(dataframe, str) or dataframe is None:
            logger.warning(f"No data to insert into {table_name} (received string/None)")
            return ""

        if dataframe.empty:
            logger.warning(f"No data to insert into {table_name}")
            return ""

        table_id = f"{self.project_id}.{self.dataset_id}.{table_name}"
        logger.info(f"Inserting {len(dataframe)} records into {table_id}")

        # Add ingestion timestamp if not present
        if 'ingestion_timestamp' not in dataframe.columns:
            dataframe = dataframe.copy()
            dataframe['ingestion_timestamp'] = pd.Timestamp.now(tz="UTC")

        # Configure load job
        job_config = bigquery.LoadJobConfig(
            write_disposition=write_disposition,
        )

        # Only add schema update options for WRITE_APPEND
        if write_disposition == "WRITE_APPEND":
            job_config.schema_update_options = [bigquery.SchemaUpdateOption.ALLOW_FIELD_ADDITION]

        # Load data
        job = self.client.load_table_from_dataframe(dataframe, table_id, job_config=job_config)
        job.result()  # Wait for job to complete

        logger.info(f"Successfully inserted {len(dataframe)} records. Job ID: {job.job_id}")
        return job.job_id
