#!/bin/bash
# ABOUTME: Starts the ingestion guardian service with automatic restarts
# ABOUTME: Ensures the guardian keeps running even if it crashes

set -e

echo "🛡️  Starting J-Quants Ingestion Guardian Service"
echo "📊 This service will monitor and ensure continuous data ingestion"
echo "⏰ Auto-triggers ingestion if idle for 15+ minutes"
echo "🔄 Auto-restarts if the guardian crashes"
echo "❌ Press Ctrl+C to stop"
echo ""

# Function to cleanup on exit
cleanup() {
    echo ""
    echo "🛑 Stopping guardian service..."
    pkill -f "python.*ingestion_guardian.py" || true
    exit 0
}

# Set up trap for clean exit
trap cleanup INT TERM

# Keep the guardian running
while true; do
    echo "🚀 Starting guardian process..."
    
    # Run the guardian
    python ingestion_guardian.py || {
        exit_code=$?
        echo "⚠️  Guardian exited with code $exit_code"
        
        # If it was a keyboard interrupt, exit cleanly
        if [ $exit_code -eq 130 ]; then
            echo "👋 Keyboard interrupt detected, exiting..."
            break
        fi
        
        echo "🔄 Restarting guardian in 30 seconds..."
        sleep 30
    }
done

echo "👋 Guardian service stopped"